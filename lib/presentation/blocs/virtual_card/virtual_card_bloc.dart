import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/virtual_card/create_virtual_card_usecase.dart';
import '../../../domain/usecases/virtual_card/get_virtual_cards_usecase.dart';
import '../../../domain/usecases/virtual_card/manage_virtual_card_usecase.dart';
import '../../../domain/usecases/virtual_card/spending_control_usecase.dart';
import 'virtual_card_event.dart';
import 'virtual_card_state.dart';

class VirtualCardBloc extends Bloc<VirtualCardEvent, VirtualCardState> {
  final CreateVirtualCardUseCase createVirtualCardUseCase;
  final GetVirtualCardsByWalletUseCase getVirtualCardsByWalletUseCase;
  final GetVirtualCardsByUserUseCase getVirtualCardsByUserUseCase;
  final GetVirtualCardUseCase getVirtualCardUseCase;
  final UpdateVirtualCardUseCase updateVirtualCardUseCase;
  final DeleteVirtualCardUseCase deleteVirtualCardUseCase;
  final ActivateCardUseCase activateCardUseCase;
  final DeactivateCardUseCase deactivateCardUseCase;
  final CancelCardUseCase cancelCardUseCase;
  final CreateSpendingControlUseCase createSpendingControlUseCase;
  final GetSpendingControlsUseCase getSpendingControlsUseCase;
  final UpdateSpendingControlUseCase updateSpendingControlUseCase;
  final DeleteSpendingControlUseCase deleteSpendingControlUseCase;
  final GetCardDetailsUseCase getCardDetailsUseCase;
  final AuthorizeTransactionUseCase authorizeTransactionUseCase;

  VirtualCardBloc({
    required this.createVirtualCardUseCase,
    required this.getVirtualCardsByWalletUseCase,
    required this.getVirtualCardsByUserUseCase,
    required this.getVirtualCardUseCase,
    required this.updateVirtualCardUseCase,
    required this.deleteVirtualCardUseCase,
    required this.activateCardUseCase,
    required this.deactivateCardUseCase,
    required this.cancelCardUseCase,
    required this.createSpendingControlUseCase,
    required this.getSpendingControlsUseCase,
    required this.updateSpendingControlUseCase,
    required this.deleteSpendingControlUseCase,
    required this.getCardDetailsUseCase,
    required this.authorizeTransactionUseCase,
  }) : super(const VirtualCardState()) {
    on<CreateVirtualCardRequested>(_onCreateVirtualCardRequested);
    on<LoadVirtualCardsRequested>(_onLoadVirtualCardsRequested);
    on<LoadVirtualCardRequested>(_onLoadVirtualCardRequested);
    on<UpdateVirtualCardRequested>(_onUpdateVirtualCardRequested);
    on<DeleteVirtualCardRequested>(_onDeleteVirtualCardRequested);
    on<ActivateCardRequested>(_onActivateCardRequested);
    on<DeactivateCardRequested>(_onDeactivateCardRequested);
    on<CancelCardRequested>(_onCancelCardRequested);
    on<CreateSpendingControlRequested>(_onCreateSpendingControlRequested);
    on<LoadSpendingControlsRequested>(_onLoadSpendingControlsRequested);
    on<UpdateSpendingControlRequested>(_onUpdateSpendingControlRequested);
    on<DeleteSpendingControlRequested>(_onDeleteSpendingControlRequested);
    on<LoadCardDetailsRequested>(_onLoadCardDetailsRequested);
    on<AuthorizeTransactionRequested>(_onAuthorizeTransactionRequested);
    on<VirtualCardReset>(_onVirtualCardReset);
  }

  Future<void> _onCreateVirtualCardRequested(
    CreateVirtualCardRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.copyWith(status: VirtualCardBlocStatus.creating));

    final result = await createVirtualCardUseCase(
        CreateVirtualCardParams(request: event.request));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (card) => emit(state.copyWith(
        status: VirtualCardBlocStatus.created,
        cards: [...state.cards, card],
        selectedCard: card,
      )),
    );
  }

  Future<void> _onLoadVirtualCardsRequested(
    LoadVirtualCardsRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.setLoading());

    if (event.walletId != null) {
      final result = await getVirtualCardsByWalletUseCase(
        GetVirtualCardsByWalletParams(walletId: event.walletId!),
      );

      result.fold(
        (failure) => emit(state.setError(failure.message)),
        (cards) => emit(state.copyWith(
          status: VirtualCardBlocStatus.loaded,
          cards: cards,
        )),
      );
    } else if (event.userId != null) {
      final result = await getVirtualCardsByUserUseCase(
        GetVirtualCardsByUserParams(userId: event.userId!),
      );

      result.fold(
        (failure) => emit(state.setError(failure.message)),
        (cards) => emit(state.copyWith(
          status: VirtualCardBlocStatus.loaded,
          cards: cards,
        )),
      );
    } else {
      emit(state.setError('Either walletId or userId must be provided'));
    }
  }

  Future<void> _onLoadVirtualCardRequested(
    LoadVirtualCardRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.setLoading());

    final result =
        await getVirtualCardUseCase(GetVirtualCardParams(cardId: event.cardId));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (card) => emit(state.copyWith(
        status: VirtualCardBlocStatus.loaded,
        selectedCard: card,
      )),
    );
  }

  Future<void> _onUpdateVirtualCardRequested(
    UpdateVirtualCardRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.copyWith(status: VirtualCardBlocStatus.updating));

    final result = await updateVirtualCardUseCase(
        UpdateVirtualCardParams(request: event.request));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (card) {
        final updatedCards =
            state.cards.map((c) => c.id == card.id ? card : c).toList();
        emit(state.copyWith(
          status: VirtualCardBlocStatus.updated,
          cards: updatedCards,
          selectedCard:
              state.selectedCard?.id == card.id ? card : state.selectedCard,
        ));
      },
    );
  }

  Future<void> _onDeleteVirtualCardRequested(
    DeleteVirtualCardRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.copyWith(status: VirtualCardBlocStatus.deleting));

    final result = await deleteVirtualCardUseCase(
        DeleteVirtualCardParams(cardId: event.cardId));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (_) {
        final updatedCards =
            state.cards.where((c) => c.id != event.cardId).toList();
        emit(state.copyWith(
          status: VirtualCardBlocStatus.deleted,
          cards: updatedCards,
          selectedCard: state.selectedCard?.id == event.cardId
              ? null
              : state.selectedCard,
        ));
      },
    );
  }

  Future<void> _onActivateCardRequested(
    ActivateCardRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.copyWith(status: VirtualCardBlocStatus.updating));

    final result =
        await activateCardUseCase(ActivateCardParams(cardId: event.cardId));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (card) {
        final updatedCards =
            state.cards.map((c) => c.id == card.id ? card : c).toList();
        emit(state.copyWith(
          status: VirtualCardBlocStatus.updated,
          cards: updatedCards,
          selectedCard:
              state.selectedCard?.id == card.id ? card : state.selectedCard,
        ));
      },
    );
  }

  Future<void> _onDeactivateCardRequested(
    DeactivateCardRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.copyWith(status: VirtualCardBlocStatus.updating));

    final result =
        await deactivateCardUseCase(DeactivateCardParams(cardId: event.cardId));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (card) {
        final updatedCards =
            state.cards.map((c) => c.id == card.id ? card : c).toList();
        emit(state.copyWith(
          status: VirtualCardBlocStatus.updated,
          cards: updatedCards,
          selectedCard:
              state.selectedCard?.id == card.id ? card : state.selectedCard,
        ));
      },
    );
  }

  Future<void> _onCancelCardRequested(
    CancelCardRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.copyWith(status: VirtualCardBlocStatus.updating));

    final result =
        await cancelCardUseCase(CancelCardParams(cardId: event.cardId));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (card) {
        final updatedCards =
            state.cards.map((c) => c.id == card.id ? card : c).toList();
        emit(state.copyWith(
          status: VirtualCardBlocStatus.updated,
          cards: updatedCards,
          selectedCard:
              state.selectedCard?.id == card.id ? card : state.selectedCard,
        ));
      },
    );
  }

  Future<void> _onCreateSpendingControlRequested(
    CreateSpendingControlRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.setLoading());

    final result = await createSpendingControlUseCase(
        CreateSpendingControlParams(request: event.request));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (control) => emit(state.copyWith(
        status: VirtualCardBlocStatus.loaded,
        spendingControls: [...state.spendingControls, control],
      )),
    );
  }

  Future<void> _onLoadSpendingControlsRequested(
    LoadSpendingControlsRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.setLoading());

    final result = await getSpendingControlsUseCase(
        GetSpendingControlsParams(cardId: event.cardId));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (controls) => emit(state.copyWith(
        status: VirtualCardBlocStatus.loaded,
        spendingControls: controls,
      )),
    );
  }

  Future<void> _onUpdateSpendingControlRequested(
    UpdateSpendingControlRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.setLoading());

    final result = await updateSpendingControlUseCase(
      UpdateSpendingControlParams(
          controlId: event.controlId, request: event.request),
    );

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (control) {
        final updatedControls = state.spendingControls
            .map((c) => c.id == control.id ? control : c)
            .toList();
        emit(state.copyWith(
          status: VirtualCardBlocStatus.loaded,
          spendingControls: updatedControls,
        ));
      },
    );
  }

  Future<void> _onDeleteSpendingControlRequested(
    DeleteSpendingControlRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.setLoading());

    final result = await deleteSpendingControlUseCase(
        DeleteSpendingControlParams(controlId: event.controlId));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (_) {
        final updatedControls = state.spendingControls
            .where((c) => c.id != event.controlId)
            .toList();
        emit(state.copyWith(
          status: VirtualCardBlocStatus.loaded,
          spendingControls: updatedControls,
        ));
      },
    );
  }

  Future<void> _onLoadCardDetailsRequested(
    LoadCardDetailsRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.setLoading());

    final result =
        await getCardDetailsUseCase(GetCardDetailsParams(cardId: event.cardId));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (details) => emit(state.copyWith(
        status: VirtualCardBlocStatus.loaded,
        cardDetails: details,
      )),
    );
  }

  Future<void> _onAuthorizeTransactionRequested(
    AuthorizeTransactionRequested event,
    Emitter<VirtualCardState> emit,
  ) async {
    emit(state.setLoading());

    final result = await authorizeTransactionUseCase(AuthorizeTransactionParams(
      cardId: event.cardId,
      amount: event.amount,
      currency: event.currency,
      merchantName: event.merchantName,
      merchantCategory: event.merchantCategory,
      metadata: event.metadata,
    ));

    result.fold(
      (failure) => emit(state.setError(failure.message)),
      (authorized) => emit(state.copyWith(
        status: VirtualCardBlocStatus.loaded,
        transactionAuthorized: authorized,
      )),
    );
  }

  void _onVirtualCardReset(
    VirtualCardReset event,
    Emitter<VirtualCardState> emit,
  ) {
    emit(const VirtualCardState());
  }
}
