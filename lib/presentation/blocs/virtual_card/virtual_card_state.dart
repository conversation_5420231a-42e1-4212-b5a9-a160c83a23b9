import 'package:equatable/equatable.dart';
import '../../../domain/entities/virtual_card.dart';

enum VirtualCardBlocStatus {
  initial,
  loading,
  loaded,
  creating,
  created,
  updating,
  updated,
  deleting,
  deleted,
  error,
}

class VirtualCardState extends Equatable {
  final VirtualCardBlocStatus status;
  final List<VirtualCard> cards;
  final VirtualCard? selectedCard;
  final List<SpendingControl> spendingControls;
  final Map<String, dynamic>? cardDetails;
  final bool? transactionAuthorized;
  final String? errorMessage;

  const VirtualCardState({
    this.status = VirtualCardBlocStatus.initial,
    this.cards = const [],
    this.selectedCard,
    this.spendingControls = const [],
    this.cardDetails,
    this.transactionAuthorized,
    this.errorMessage,
  });

  VirtualCardState copyWith({
    VirtualCardBlocStatus? status,
    List<VirtualCard>? cards,
    VirtualCard? selectedCard,
    List<SpendingControl>? spendingControls,
    Map<String, dynamic>? cardDetails,
    bool? transactionAuthorized,
    String? errorMessage,
  }) {
    return VirtualCardState(
      status: status ?? this.status,
      cards: cards ?? this.cards,
      selectedCard: selectedCard ?? this.selectedCard,
      spendingControls: spendingControls ?? this.spendingControls,
      cardDetails: cardDetails ?? this.cardDetails,
      transactionAuthorized:
          transactionAuthorized ?? this.transactionAuthorized,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  VirtualCardState clearError() {
    return copyWith(
      status: VirtualCardBlocStatus.initial,
      errorMessage: null,
    );
  }

  VirtualCardState setLoading() {
    return copyWith(
      status: VirtualCardBlocStatus.loading,
      errorMessage: null,
    );
  }

  VirtualCardState setError(String message) {
    return copyWith(
      status: VirtualCardBlocStatus.error,
      errorMessage: message,
    );
  }

  @override
  List<Object?> get props => [
        status,
        cards,
        selectedCard,
        spendingControls,
        cardDetails,
        transactionAuthorized,
        errorMessage,
      ];
}
