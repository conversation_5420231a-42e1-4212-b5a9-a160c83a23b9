import 'package:equatable/equatable.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;
  final bool rememberMe;

  const AuthLoginRequested({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [email, password, rememberMe];
}

class AuthRegisterRequested extends AuthEvent {
  final String email;
  final String password;
  final String confirmPassword;
  final String firstName;
  final String lastName;
  final String? phoneNumber;

  const AuthRegisterRequested({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
  });

  @override
  List<Object?> get props => [
        email,
        password,
        confirmPassword,
        firstName,
        lastName,
        phoneNumber,
      ];
}

class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

class AuthPasswordResetRequested extends AuthEvent {
  final String email;

  const AuthPasswordResetRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

class AuthPasswordUpdateRequested extends AuthEvent {
  final String currentPassword;
  final String newPassword;

  const AuthPasswordUpdateRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

class AuthProfileUpdateRequested extends AuthEvent {
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? avatarUrl;

  const AuthProfileUpdateRequested({
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.avatarUrl,
  });

  @override
  List<Object?> get props => [firstName, lastName, phoneNumber, avatarUrl];
}

class AuthBiometricToggleRequested extends AuthEvent {
  final bool enabled;

  const AuthBiometricToggleRequested({required this.enabled});

  @override
  List<Object?> get props => [enabled];
}

class AuthBiometricLoginRequested extends AuthEvent {
  const AuthBiometricLoginRequested();
}

class AuthSessionExpired extends AuthEvent {
  const AuthSessionExpired();
}

class AuthErrorCleared extends AuthEvent {
  const AuthErrorCleared();
}
