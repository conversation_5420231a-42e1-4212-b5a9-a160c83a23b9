import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/usecases/usecase.dart';
import '../../../domain/usecases/auth/login_usecase.dart';
import '../../../domain/usecases/auth/register_usecase.dart';
import '../../../domain/usecases/auth/logout_usecase.dart';
import '../../../domain/usecases/auth/get_current_user_usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  final RegisterUseCase _registerUseCase;
  final LogoutUseCase _logoutUseCase;
  final GetCurrentUserUseCase _getCurrentUserUseCase;

  AuthBloc({
    required LoginUseCase loginUseCase,
    required RegisterUseCase registerUseCase,
    required LogoutUseCase logoutUseCase,
    required GetCurrentUserUseCase getCurrentUserUseCase,
  })  : _loginUseCase = loginUseCase,
        _registerUseCase = registerUseCase,
        _logoutUseCase = logoutUseCase,
        _getCurrentUserUseCase = getCurrentUserUseCase,
        super(const AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthRegisterRequested>(_onAuthRegisterRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthPasswordResetRequested>(_onAuthPasswordResetRequested);
    on<AuthPasswordUpdateRequested>(_onAuthPasswordUpdateRequested);
    on<AuthProfileUpdateRequested>(_onAuthProfileUpdateRequested);
    on<AuthBiometricToggleRequested>(_onAuthBiometricToggleRequested);
    on<AuthBiometricLoginRequested>(_onAuthBiometricLoginRequested);
    on<AuthSessionExpired>(_onAuthSessionExpired);
    on<AuthErrorCleared>(_onAuthErrorCleared);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _getCurrentUserUseCase(NoParams());

    result.fold(
      (failure) => emit(const AuthUnauthenticated()),
      (user) {
        if (user != null) {
          emit(AuthAuthenticated(user: user));
        } else {
          emit(const AuthUnauthenticated());
        }
      },
    );
  }

  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _loginUseCase(
      LoginParams(
        email: event.email,
        password: event.password,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(errorMessage: failure.message)),
      (user) => emit(AuthAuthenticated(
        user: user,
        rememberMe: event.rememberMe,
      )),
    );
  }

  Future<void> _onAuthRegisterRequested(
    AuthRegisterRequested event,
    Emitter<AuthState> emit,
  ) async {
    // Validate password confirmation
    if (event.password != event.confirmPassword) {
      emit(const AuthError(errorMessage: 'Passwords do not match'));
      return;
    }

    emit(const AuthRegistrationLoading());

    final result = await _registerUseCase(
      RegisterParams(
        email: event.email,
        password: event.password,
        firstName: event.firstName,
        lastName: event.lastName,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(errorMessage: failure.message)),
      (user) => emit(const AuthRegistrationSuccess(
        message: 'Registration successful! Please check your email to verify your account.',
      )),
    );
  }

  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _logoutUseCase(NoParams());

    result.fold(
      (failure) => emit(AuthError(
        errorMessage: failure.message,
        user: state.user,
      )),
      (_) => emit(const AuthUnauthenticated()),
    );
  }

  Future<void> _onAuthPasswordResetRequested(
    AuthPasswordResetRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthPasswordResetLoading());

    // TODO: Implement password reset use case
    await Future.delayed(const Duration(seconds: 1));

    emit(const AuthPasswordResetSuccess(
      message: 'Password reset email sent. Please check your inbox.',
    ));
  }

  Future<void> _onAuthPasswordUpdateRequested(
    AuthPasswordUpdateRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state.user == null) return;

    emit(AuthProfileUpdateLoading(user: state.user!));

    // TODO: Implement password update use case
    await Future.delayed(const Duration(seconds: 1));

    emit(AuthProfileUpdateSuccess(
      updatedUser: state.user!,
      message: 'Password updated successfully',
    ));
  }

  Future<void> _onAuthProfileUpdateRequested(
    AuthProfileUpdateRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state.user == null) return;

    emit(AuthProfileUpdateLoading(user: state.user!));

    // TODO: Implement profile update use case
    await Future.delayed(const Duration(seconds: 1));

    // Create updated user with new information
    final updatedUser = state.user!.copyWith(
      firstName: event.firstName ?? state.user!.firstName,
      lastName: event.lastName ?? state.user!.lastName,
      phoneNumber: event.phoneNumber ?? state.user!.phoneNumber,
      avatarUrl: event.avatarUrl ?? state.user!.avatarUrl,
    );

    emit(AuthProfileUpdateSuccess(
      updatedUser: updatedUser,
      message: 'Profile updated successfully',
    ));
  }

  Future<void> _onAuthBiometricToggleRequested(
    AuthBiometricToggleRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state.user == null) return;

    // TODO: Implement biometric settings persistence
    emit(state.copyWith(biometricEnabled: event.enabled));
  }

  Future<void> _onAuthBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    // TODO: Implement biometric authentication
    await Future.delayed(const Duration(seconds: 1));

    final result = await _getCurrentUserUseCase(NoParams());

    result.fold(
      (failure) => emit(AuthError(errorMessage: failure.message)),
      (user) {
        if (user != null) {
          emit(AuthAuthenticated(user: user, biometricEnabled: true));
        } else {
          emit(const AuthError(errorMessage: 'Biometric authentication failed'));
        }
      },
    );
  }

  Future<void> _onAuthSessionExpired(
    AuthSessionExpired event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthUnauthenticated(
      errorMessage: 'Your session has expired. Please log in again.',
    ));
  }

  Future<void> _onAuthErrorCleared(
    AuthErrorCleared event,
    Emitter<AuthState> emit,
  ) async {
    if (state.hasError) {
      if (state.user != null) {
        emit(AuthAuthenticated(user: state.user!));
      } else {
        emit(const AuthUnauthenticated());
      }
    }
  }
}
