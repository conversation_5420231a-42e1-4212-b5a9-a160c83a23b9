import 'package:equatable/equatable.dart';
import '../../../domain/entities/user.dart';

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthState extends Equatable {
  final AuthStatus status;
  final User? user;
  final String? errorMessage;
  final bool isLoading;
  final bool biometricEnabled;
  final bool rememberMe;

  const AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.errorMessage,
    this.isLoading = false,
    this.biometricEnabled = false,
    this.rememberMe = false,
  });

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? errorMessage,
    bool? isLoading,
    bool? biometricEnabled,
    bool? rememberMe,
    bool clearError = false,
    bool clearUser = false,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: clearUser ? null : (user ?? this.user),
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      isLoading: isLoading ?? this.isLoading,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      rememberMe: rememberMe ?? this.rememberMe,
    );
  }

  @override
  List<Object?> get props => [
        status,
        user,
        errorMessage,
        isLoading,
        biometricEnabled,
        rememberMe,
      ];

  // Convenience getters
  bool get isAuthenticated => status == AuthStatus.authenticated && user != null;
  bool get isUnauthenticated => status == AuthStatus.unauthenticated;
  bool get hasError => status == AuthStatus.error && errorMessage != null;
  bool get isInitial => status == AuthStatus.initial;
}

// Specific state classes for better type safety
class AuthInitial extends AuthState {
  const AuthInitial() : super(status: AuthStatus.initial);
}

class AuthLoading extends AuthState {
  const AuthLoading() : super(status: AuthStatus.loading, isLoading: true);
}

class AuthAuthenticated extends AuthState {
  const AuthAuthenticated({
    required User user,
    bool biometricEnabled = false,
    bool rememberMe = false,
  }) : super(
          status: AuthStatus.authenticated,
          user: user,
          biometricEnabled: biometricEnabled,
          rememberMe: rememberMe,
        );
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated({String? errorMessage})
      : super(
          status: AuthStatus.unauthenticated,
          errorMessage: errorMessage,
        );
}

class AuthError extends AuthState {
  const AuthError({
    required String errorMessage,
    User? user,
  }) : super(
          status: AuthStatus.error,
          errorMessage: errorMessage,
          user: user,
        );
}

// Registration specific states
class AuthRegistrationLoading extends AuthState {
  const AuthRegistrationLoading()
      : super(status: AuthStatus.loading, isLoading: true);
}

class AuthRegistrationSuccess extends AuthState {
  final String message;

  const AuthRegistrationSuccess({
    required this.message,
  }) : super(status: AuthStatus.unauthenticated);

  @override
  List<Object?> get props => [...super.props, message];
}

// Password reset specific states
class AuthPasswordResetLoading extends AuthState {
  const AuthPasswordResetLoading()
      : super(status: AuthStatus.loading, isLoading: true);
}

class AuthPasswordResetSuccess extends AuthState {
  final String message;

  const AuthPasswordResetSuccess({
    required this.message,
  }) : super(status: AuthStatus.unauthenticated);

  @override
  List<Object?> get props => [...super.props, message];
}

// Profile update specific states
class AuthProfileUpdateLoading extends AuthState {
  const AuthProfileUpdateLoading({required User user})
      : super(
          status: AuthStatus.authenticated,
          user: user,
          isLoading: true,
        );
}

class AuthProfileUpdateSuccess extends AuthState {
  final User updatedUser;
  final String message;

  const AuthProfileUpdateSuccess({
    required this.updatedUser,
    required this.message,
  }) : super(
          status: AuthStatus.authenticated,
          user: updatedUser,
        );

  @override
  List<Object?> get props => [...super.props, message];
}
