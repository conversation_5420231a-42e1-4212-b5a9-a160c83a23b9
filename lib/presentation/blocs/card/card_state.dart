import 'package:equatable/equatable.dart';
import '../../../domain/entities/virtual_card.dart';

enum CardStatus {
  initial,
  loading,
  loaded,
  creating,
  created,
  updating,
  updated,
  deleting,
  deleted,
  error,
}

class CardState extends Equatable {
  final CardStatus status;
  final List<VirtualCard> cards;
  final VirtualCard? selectedCard;
  final Map<String, dynamic>? cardDetails;
  final List<Map<String, dynamic>> transactions;
  final String? errorMessage;
  final bool isLoading;

  const CardState({
    this.status = CardStatus.initial,
    this.cards = const [],
    this.selectedCard,
    this.cardDetails,
    this.transactions = const [],
    this.errorMessage,
    this.isLoading = false,
  });

  CardState copyWith({
    CardStatus? status,
    List<VirtualCard>? cards,
    VirtualCard? selectedCard,
    Map<String, dynamic>? cardDetails,
    List<Map<String, dynamic>>? transactions,
    String? errorMessage,
    bool? isLoading,
    bool clearError = false,
    bool clearSelectedCard = false,
    bool clearCardDetails = false,
  }) {
    return CardState(
      status: status ?? this.status,
      cards: cards ?? this.cards,
      selectedCard: clearSelectedCard ? null : (selectedCard ?? this.selectedCard),
      cardDetails: clearCardDetails ? null : (cardDetails ?? this.cardDetails),
      transactions: transactions ?? this.transactions,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props => [
        status,
        cards,
        selectedCard,
        cardDetails,
        transactions,
        errorMessage,
        isLoading,
      ];

  // Convenience getters
  bool get hasCards => cards.isNotEmpty;
  bool get hasError => status == CardStatus.error && errorMessage != null;
  bool get isInitial => status == CardStatus.initial;
  bool get hasSelectedCard => selectedCard != null;
  bool get hasCardDetails => cardDetails != null;
  bool get hasTransactions => transactions.isNotEmpty;
}

// Specific state classes for better type safety
class CardInitial extends CardState {
  const CardInitial() : super(status: CardStatus.initial);
}

class CardLoading extends CardState {
  const CardLoading() : super(status: CardStatus.loading, isLoading: true);
}

class CardLoaded extends CardState {
  const CardLoaded({
    required List<VirtualCard> cards,
  }) : super(
          status: CardStatus.loaded,
          cards: cards,
        );
}

class CardCreating extends CardState {
  const CardCreating({
    required List<VirtualCard> cards,
  }) : super(
          status: CardStatus.creating,
          cards: cards,
          isLoading: true,
        );
}

class CardCreated extends CardState {
  final VirtualCard newCard;
  final String message;

  const CardCreated({
    required List<VirtualCard> cards,
    required this.newCard,
    required this.message,
  }) : super(
          status: CardStatus.created,
          cards: cards,
          selectedCard: newCard,
        );

  @override
  List<Object?> get props => [...super.props, newCard, message];
}

class CardUpdating extends CardState {
  const CardUpdating({
    required List<VirtualCard> cards,
    VirtualCard? selectedCard,
  }) : super(
          status: CardStatus.updating,
          cards: cards,
          selectedCard: selectedCard,
          isLoading: true,
        );
}

class CardUpdated extends CardState {
  final VirtualCard updatedCard;
  final String message;

  const CardUpdated({
    required List<VirtualCard> cards,
    required this.updatedCard,
    required this.message,
  }) : super(
          status: CardStatus.updated,
          cards: cards,
          selectedCard: updatedCard,
        );

  @override
  List<Object?> get props => [...super.props, updatedCard, message];
}

class CardDeleting extends CardState {
  const CardDeleting({
    required List<VirtualCard> cards,
    VirtualCard? selectedCard,
  }) : super(
          status: CardStatus.deleting,
          cards: cards,
          selectedCard: selectedCard,
          isLoading: true,
        );
}

class CardDeleted extends CardState {
  final String message;

  const CardDeleted({
    required List<VirtualCard> cards,
    required this.message,
  }) : super(
          status: CardStatus.deleted,
          cards: cards,
        );

  @override
  List<Object?> get props => [...super.props, message];
}

class CardError extends CardState {
  const CardError({
    required String errorMessage,
    List<VirtualCard> cards = const [],
    VirtualCard? selectedCard,
  }) : super(
          status: CardStatus.error,
          cards: cards,
          selectedCard: selectedCard,
          errorMessage: errorMessage,
        );
}

class CardDetailsLoaded extends CardState {
  const CardDetailsLoaded({
    required List<VirtualCard> cards,
    required VirtualCard selectedCard,
    required Map<String, dynamic> cardDetails,
  }) : super(
          status: CardStatus.loaded,
          cards: cards,
          selectedCard: selectedCard,
          cardDetails: cardDetails,
        );
}

class CardTransactionsLoaded extends CardState {
  const CardTransactionsLoaded({
    required List<VirtualCard> cards,
    required VirtualCard selectedCard,
    required List<Map<String, dynamic>> transactions,
  }) : super(
          status: CardStatus.loaded,
          cards: cards,
          selectedCard: selectedCard,
          transactions: transactions,
        );
}
