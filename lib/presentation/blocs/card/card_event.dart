import 'package:equatable/equatable.dart';

abstract class Card<PERSON>vent extends Equatable {
  const CardEvent();

  @override
  List<Object?> get props => [];
}

class CardLoadRequested extends CardEvent {
  final String walletId;

  const CardLoadRequested({required this.walletId});

  @override
  List<Object?> get props => [walletId];
}

class CardCreateRequested extends Card<PERSON>vent {
  final String walletId;
  final String cardType;
  final double? spendingLimit;
  final Map<String, dynamic>? metadata;

  const CardCreateRequested({
    required this.walletId,
    required this.cardType,
    this.spendingLimit,
    this.metadata,
  });

  @override
  List<Object?> get props => [walletId, cardType, spendingLimit, metadata];
}

class CardActivateRequested extends CardEvent {
  final String cardId;

  const CardActivateRequested({required this.cardId});

  @override
  List<Object?> get props => [cardId];
}

class CardDeactivateRequested extends Card<PERSON>vent {
  final String cardId;

  const CardDeactivateRequested({required this.cardId});

  @override
  List<Object?> get props => [cardId];
}

class CardDeleteRequested extends Card<PERSON>vent {
  final String cardId;

  const CardDeleteRequested({required this.cardId});

  @override
  List<Object?> get props => [cardId];
}

class CardDetailsRequested extends CardEvent {
  final String cardId;

  const CardDetailsRequested({required this.cardId});

  @override
  List<Object?> get props => [cardId];
}

class CardSpendingLimitUpdateRequested extends CardEvent {
  final String cardId;
  final double newLimit;

  const CardSpendingLimitUpdateRequested({
    required this.cardId,
    required this.newLimit,
  });

  @override
  List<Object?> get props => [cardId, newLimit];
}

class CardTransactionsRequested extends CardEvent {
  final String cardId;
  final int? limit;
  final String? startingAfter;

  const CardTransactionsRequested({
    required this.cardId,
    this.limit,
    this.startingAfter,
  });

  @override
  List<Object?> get props => [cardId, limit, startingAfter];
}

class CardPinUpdateRequested extends CardEvent {
  final String cardId;
  final String newPin;

  const CardPinUpdateRequested({
    required this.cardId,
    required this.newPin,
  });

  @override
  List<Object?> get props => [cardId, newPin];
}

class CardFreezeRequested extends CardEvent {
  final String cardId;
  final String reason;

  const CardFreezeRequested({
    required this.cardId,
    required this.reason,
  });

  @override
  List<Object?> get props => [cardId, reason];
}

class CardUnfreezeRequested extends CardEvent {
  final String cardId;

  const CardUnfreezeRequested({required this.cardId});

  @override
  List<Object?> get props => [cardId];
}

class CardErrorCleared extends CardEvent {
  const CardErrorCleared();
}
