import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/usecases/usecase.dart';
import '../../../domain/usecases/virtual_card/create_virtual_card_usecase.dart';
import '../../../domain/usecases/virtual_card/get_virtual_cards_usecase.dart';
import '../../../domain/usecases/virtual_card/manage_virtual_card_usecase.dart';
import '../../../domain/entities/virtual_card.dart';
import 'card_event.dart';
import 'card_state.dart';

class CardBloc extends Bloc<CardEvent, CardState> {
  final CreateVirtualCardUseCase _createVirtualCardUseCase;
  final GetVirtualCardsByWalletUseCase _getVirtualCardsUseCase;
  final UpdateVirtualCardUseCase _updateVirtualCardUseCase;
  final DeleteVirtualCardUseCase _deleteVirtualCardUseCase;
  final GetCardDetailsUseCase _getCardDetailsUseCase;

  CardBloc({
    required CreateVirtualCardUseCase createVirtualCardUseCase,
    required GetVirtualCardsByWalletUseCase getVirtualCardsUseCase,
    required UpdateVirtualCardUseCase updateVirtualCardUseCase,
    required DeleteVirtualCardUseCase deleteVirtualCardUseCase,
    required GetCardDetailsUseCase getCardDetailsUseCase,
  })  : _createVirtualCardUseCase = createVirtualCardUseCase,
        _getVirtualCardsUseCase = getVirtualCardsUseCase,
        _updateVirtualCardUseCase = updateVirtualCardUseCase,
        _deleteVirtualCardUseCase = deleteVirtualCardUseCase,
        _getCardDetailsUseCase = getCardDetailsUseCase,
        super(const CardInitial()) {
    on<CardLoadRequested>(_onCardLoadRequested);
    on<CardCreateRequested>(_onCardCreateRequested);
    on<CardActivateRequested>(_onCardActivateRequested);
    on<CardDeactivateRequested>(_onCardDeactivateRequested);
    on<CardDeleteRequested>(_onCardDeleteRequested);
    on<CardDetailsRequested>(_onCardDetailsRequested);
    on<CardSpendingLimitUpdateRequested>(_onCardSpendingLimitUpdateRequested);
    on<CardTransactionsRequested>(_onCardTransactionsRequested);
    on<CardPinUpdateRequested>(_onCardPinUpdateRequested);
    on<CardFreezeRequested>(_onCardFreezeRequested);
    on<CardUnfreezeRequested>(_onCardUnfreezeRequested);
    on<CardErrorCleared>(_onCardErrorCleared);
  }

  Future<void> _onCardLoadRequested(
    CardLoadRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(const CardLoading());

    final result = await _getVirtualCardsUseCase(
        GetVirtualCardsByWalletParams(walletId: event.walletId));

    result.fold(
      (failure) => emit(CardError(errorMessage: failure.message)),
      (cards) => emit(CardLoaded(cards: cards)),
    );
  }

  Future<void> _onCardCreateRequested(
    CardCreateRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardCreating(cards: state.cards));

    // Convert string cardType to VirtualCardType enum
    final cardType = event.cardType.toLowerCase() == 'physical'
        ? VirtualCardType.physical
        : VirtualCardType.virtual;

    // Create spending controls if spending limit is provided
    final spendingControls = <SpendingControl>[];
    if (event.spendingLimit != null) {
      spendingControls.add(SpendingControl(
        id: '', // Will be generated by the backend
        cardId: '', // Will be set by the backend
        type: SpendingControlType.amount,
        amount: event.spendingLimit,
        interval: SpendingInterval.monthly,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ));
    }

    final request = CreateVirtualCardRequest(
      walletId: event.walletId,
      userId: 'current_user_id', // TODO: Get from auth state
      cardholderName: 'Card Holder', // TODO: Get from user profile
      type: cardType,
      spendingControls: spendingControls,
      metadata: event.metadata,
    );

    final result = await _createVirtualCardUseCase(
      CreateVirtualCardParams(request: request),
    );

    result.fold(
      (failure) => emit(CardError(
        errorMessage: failure.message,
        cards: state.cards,
      )),
      (newCard) {
        final updatedCards = [...state.cards, newCard];
        emit(CardCreated(
          cards: updatedCards,
          newCard: newCard,
          message: 'Virtual card created successfully',
        ));
      },
    );
  }

  Future<void> _onCardActivateRequested(
    CardActivateRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardUpdating(
      cards: state.cards,
      selectedCard: state.selectedCard,
    ));

    final request = UpdateVirtualCardRequest(
      cardId: event.cardId,
      status: VirtualCardStatus.active,
    );

    final result = await _updateVirtualCardUseCase(
      UpdateVirtualCardParams(request: request),
    );

    result.fold(
      (failure) => emit(CardError(
        errorMessage: failure.message,
        cards: state.cards,
        selectedCard: state.selectedCard,
      )),
      (updatedCard) {
        final updatedCards = state.cards
            .map((card) => card.id == updatedCard.id ? updatedCard : card)
            .toList();
        emit(CardUpdated(
          cards: updatedCards,
          updatedCard: updatedCard,
          message: 'Card activated successfully',
        ));
      },
    );
  }

  Future<void> _onCardDeactivateRequested(
    CardDeactivateRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardUpdating(
      cards: state.cards,
      selectedCard: state.selectedCard,
    ));

    final request = UpdateVirtualCardRequest(
      cardId: event.cardId,
      status: VirtualCardStatus.inactive,
    );

    final result = await _updateVirtualCardUseCase(
      UpdateVirtualCardParams(request: request),
    );

    result.fold(
      (failure) => emit(CardError(
        errorMessage: failure.message,
        cards: state.cards,
        selectedCard: state.selectedCard,
      )),
      (updatedCard) {
        final updatedCards = state.cards
            .map((card) => card.id == updatedCard.id ? updatedCard : card)
            .toList();
        emit(CardUpdated(
          cards: updatedCards,
          updatedCard: updatedCard,
          message: 'Card deactivated successfully',
        ));
      },
    );
  }

  Future<void> _onCardDeleteRequested(
    CardDeleteRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardDeleting(
      cards: state.cards,
      selectedCard: state.selectedCard,
    ));

    final result = await _deleteVirtualCardUseCase(
      DeleteVirtualCardParams(cardId: event.cardId),
    );

    result.fold(
      (failure) => emit(CardError(
        errorMessage: failure.message,
        cards: state.cards,
        selectedCard: state.selectedCard,
      )),
      (_) {
        final updatedCards =
            state.cards.where((card) => card.id != event.cardId).toList();
        emit(CardDeleted(
          cards: updatedCards,
          message: 'Card deleted successfully',
        ));
      },
    );
  }

  Future<void> _onCardDetailsRequested(
    CardDetailsRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardLoading());

    final result = await _getCardDetailsUseCase(
      GetCardDetailsParams(cardId: event.cardId),
    );

    result.fold(
      (failure) => emit(CardError(
        errorMessage: failure.message,
        cards: state.cards,
      )),
      (cardDetails) {
        final selectedCard =
            state.cards.where((card) => card.id == event.cardId).firstOrNull;

        if (selectedCard != null) {
          emit(CardDetailsLoaded(
            cards: state.cards,
            selectedCard: selectedCard,
            cardDetails: cardDetails,
          ));
        } else {
          emit(CardError(
            errorMessage: 'Card not found',
            cards: state.cards,
          ));
        }
      },
    );
  }

  Future<void> _onCardSpendingLimitUpdateRequested(
    CardSpendingLimitUpdateRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardUpdating(
      cards: state.cards,
      selectedCard: state.selectedCard,
    ));

    // Create new spending control with the updated limit
    final spendingControl = SpendingControl(
      id: '', // Will be generated by the backend
      cardId: event.cardId,
      type: SpendingControlType.amount,
      amount: event.newLimit,
      interval: SpendingInterval.monthly,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final request = UpdateVirtualCardRequest(
      cardId: event.cardId,
      spendingControls: [spendingControl],
    );

    final result = await _updateVirtualCardUseCase(
      UpdateVirtualCardParams(request: request),
    );

    result.fold(
      (failure) => emit(CardError(
        errorMessage: failure.message,
        cards: state.cards,
        selectedCard: state.selectedCard,
      )),
      (updatedCard) {
        final updatedCards = state.cards
            .map((card) => card.id == updatedCard.id ? updatedCard : card)
            .toList();
        emit(CardUpdated(
          cards: updatedCards,
          updatedCard: updatedCard,
          message: 'Spending limit updated successfully',
        ));
      },
    );
  }

  Future<void> _onCardTransactionsRequested(
    CardTransactionsRequested event,
    Emitter<CardState> emit,
  ) async {
    // TODO: Implement transaction loading use case
    emit(CardLoading());

    // Mock implementation for now
    await Future.delayed(const Duration(seconds: 1));

    final selectedCard =
        state.cards.where((card) => card.id == event.cardId).firstOrNull;

    if (selectedCard != null) {
      emit(CardTransactionsLoaded(
        cards: state.cards,
        selectedCard: selectedCard,
        transactions: [], // TODO: Load actual transactions
      ));
    } else {
      emit(CardError(
        errorMessage: 'Card not found',
        cards: state.cards,
      ));
    }
  }

  Future<void> _onCardPinUpdateRequested(
    CardPinUpdateRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardUpdating(
      cards: state.cards,
      selectedCard: state.selectedCard,
    ));

    // TODO: Implement PIN update use case
    await Future.delayed(const Duration(seconds: 1));

    emit(CardUpdated(
      cards: state.cards,
      updatedCard: state.selectedCard!,
      message: 'PIN updated successfully',
    ));
  }

  Future<void> _onCardFreezeRequested(
    CardFreezeRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardUpdating(
      cards: state.cards,
      selectedCard: state.selectedCard,
    ));

    final request = UpdateVirtualCardRequest(
      cardId: event.cardId,
      status: VirtualCardStatus.inactive, // Using inactive as frozen equivalent
    );

    final result = await _updateVirtualCardUseCase(
      UpdateVirtualCardParams(request: request),
    );

    result.fold(
      (failure) => emit(CardError(
        errorMessage: failure.message,
        cards: state.cards,
        selectedCard: state.selectedCard,
      )),
      (updatedCard) {
        final updatedCards = state.cards
            .map((card) => card.id == updatedCard.id ? updatedCard : card)
            .toList();
        emit(CardUpdated(
          cards: updatedCards,
          updatedCard: updatedCard,
          message: 'Card frozen successfully',
        ));
      },
    );
  }

  Future<void> _onCardUnfreezeRequested(
    CardUnfreezeRequested event,
    Emitter<CardState> emit,
  ) async {
    emit(CardUpdating(
      cards: state.cards,
      selectedCard: state.selectedCard,
    ));

    final request = UpdateVirtualCardRequest(
      cardId: event.cardId,
      status: VirtualCardStatus.active,
    );

    final result = await _updateVirtualCardUseCase(
      UpdateVirtualCardParams(request: request),
    );

    result.fold(
      (failure) => emit(CardError(
        errorMessage: failure.message,
        cards: state.cards,
        selectedCard: state.selectedCard,
      )),
      (updatedCard) {
        final updatedCards = state.cards
            .map((card) => card.id == updatedCard.id ? updatedCard : card)
            .toList();
        emit(CardUpdated(
          cards: updatedCards,
          updatedCard: updatedCard,
          message: 'Card unfrozen successfully',
        ));
      },
    );
  }

  Future<void> _onCardErrorCleared(
    CardErrorCleared event,
    Emitter<CardState> emit,
  ) async {
    if (state.hasError) {
      emit(state.copyWith(clearError: true));
    }
  }
}
