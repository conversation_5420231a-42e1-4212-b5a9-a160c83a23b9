import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/user.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends User {
  @JsonKey(
    fromJson: _preferencesFromJson,
    toJson: _preferencesToJson,
  )
  @override
  final UserPreferences preferences;

  const UserModel({
    required super.id,
    required super.email,
    super.firstName,
    super.lastName,
    super.phoneNumber,
    super.avatarUrl,
    required super.createdAt,
    required super.updatedAt,
    required super.isEmailVerified,
    required super.isPhoneVerified,
    required this.preferences,
  }) : super(preferences: preferences);

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  static UserPreferences _preferencesFromJson(Map<String, dynamic> json) {
    return UserPreferencesModel.fromJson(json).toEntity();
  }

  static Map<String, dynamic> _preferencesToJson(UserPreferences preferences) {
    return UserPreferencesModel.fromEntity(preferences).toJson();
  }

  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phoneNumber: user.phoneNumber,
      avatarUrl: user.avatarUrl,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
      preferences: user.preferences,
    );
  }

  User toEntity() {
    return User(
      id: id,
      email: email,
      firstName: firstName,
      lastName: lastName,
      phoneNumber: phoneNumber,
      avatarUrl: avatarUrl,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isEmailVerified: isEmailVerified,
      isPhoneVerified: isPhoneVerified,
      preferences: preferences,
    );
  }

  @override
  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    UserPreferences? preferences,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      preferences: preferences ?? this.preferences,
    );
  }
}

@JsonSerializable()
class UserPreferencesModel extends UserPreferences {
  const UserPreferencesModel({
    required super.notificationsEnabled,
    required super.emailNotificationsEnabled,
    required super.pushNotificationsEnabled,
    required super.transactionNotificationsEnabled,
    required super.invitationNotificationsEnabled,
    required super.currency,
    required super.language,
    required super.timezone,
    required super.biometricEnabled,
    required super.darkModeEnabled,
  });

  factory UserPreferencesModel.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserPreferencesModelToJson(this);

  factory UserPreferencesModel.fromEntity(UserPreferences preferences) {
    return UserPreferencesModel(
      notificationsEnabled: preferences.notificationsEnabled,
      emailNotificationsEnabled: preferences.emailNotificationsEnabled,
      pushNotificationsEnabled: preferences.pushNotificationsEnabled,
      transactionNotificationsEnabled:
          preferences.transactionNotificationsEnabled,
      invitationNotificationsEnabled:
          preferences.invitationNotificationsEnabled,
      currency: preferences.currency,
      language: preferences.language,
      timezone: preferences.timezone,
      biometricEnabled: preferences.biometricEnabled,
      darkModeEnabled: preferences.darkModeEnabled,
    );
  }

  UserPreferences toEntity() {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled,
      pushNotificationsEnabled: pushNotificationsEnabled,
      transactionNotificationsEnabled: transactionNotificationsEnabled,
      invitationNotificationsEnabled: invitationNotificationsEnabled,
      currency: currency,
      language: language,
      timezone: timezone,
      biometricEnabled: biometricEnabled,
      darkModeEnabled: darkModeEnabled,
    );
  }

  @override
  UserPreferencesModel copyWith({
    bool? notificationsEnabled,
    bool? emailNotificationsEnabled,
    bool? pushNotificationsEnabled,
    bool? transactionNotificationsEnabled,
    bool? invitationNotificationsEnabled,
    String? currency,
    String? language,
    String? timezone,
    bool? biometricEnabled,
    bool? darkModeEnabled,
  }) {
    return UserPreferencesModel(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotificationsEnabled:
          emailNotificationsEnabled ?? this.emailNotificationsEnabled,
      pushNotificationsEnabled:
          pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      transactionNotificationsEnabled: transactionNotificationsEnabled ??
          this.transactionNotificationsEnabled,
      invitationNotificationsEnabled:
          invitationNotificationsEnabled ?? this.invitationNotificationsEnabled,
      currency: currency ?? this.currency,
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
    );
  }
}
