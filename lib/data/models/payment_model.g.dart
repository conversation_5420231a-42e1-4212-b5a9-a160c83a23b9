// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentIntentModel _$PaymentIntentModelFromJson(Map<String, dynamic> json) =>
    PaymentIntentModel(
      id: json['id'] as String,
      amount: (json['amount'] as num).toInt(),
      currency: json['currency'] as String,
      status: $enumDecode(_$PaymentIntentStatusEnumMap, json['status']),
      clientSecret: json['clientSecret'] as String,
      description: json['description'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$PaymentIntentModelToJson(PaymentIntentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': _$PaymentIntentStatusEnumMap[instance.status]!,
      'clientSecret': instance.clientSecret,
      'description': instance.description,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$PaymentIntentStatusEnumMap = {
  PaymentIntentStatus.requiresPaymentMethod: 'requiresPaymentMethod',
  PaymentIntentStatus.requiresConfirmation: 'requiresConfirmation',
  PaymentIntentStatus.requiresAction: 'requiresAction',
  PaymentIntentStatus.processing: 'processing',
  PaymentIntentStatus.requiresCapture: 'requiresCapture',
  PaymentIntentStatus.canceled: 'canceled',
  PaymentIntentStatus.succeeded: 'succeeded',
};

PaymentMethodModel _$PaymentMethodModelFromJson(Map<String, dynamic> json) =>
    PaymentMethodModel(
      id: json['id'] as String,
      type: $enumDecode(_$PaymentMethodTypeEnumMap, json['type']),
      card: PaymentMethodModel._cardFromJson(
          json['card'] as Map<String, dynamic>?),
      customerId: json['customerId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$PaymentMethodModelToJson(PaymentMethodModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$PaymentMethodTypeEnumMap[instance.type]!,
      'customerId': instance.customerId,
      'createdAt': instance.createdAt.toIso8601String(),
      'card': PaymentMethodModel._cardToJson(instance.card),
    };

const _$PaymentMethodTypeEnumMap = {
  PaymentMethodType.card: 'card',
  PaymentMethodType.bankAccount: 'bankAccount',
  PaymentMethodType.applePay: 'applePay',
  PaymentMethodType.googlePay: 'googlePay',
};

CardDetailsModel _$CardDetailsModelFromJson(Map<String, dynamic> json) =>
    CardDetailsModel(
      brand: $enumDecode(_$CardBrandEnumMap, json['brand']),
      last4: json['last4'] as String,
      expMonth: (json['expMonth'] as num).toInt(),
      expYear: (json['expYear'] as num).toInt(),
      funding: $enumDecodeNullable(_$CardFundingEnumMap, json['funding']),
      country: json['country'] as String?,
    );

Map<String, dynamic> _$CardDetailsModelToJson(CardDetailsModel instance) =>
    <String, dynamic>{
      'brand': _$CardBrandEnumMap[instance.brand]!,
      'last4': instance.last4,
      'expMonth': instance.expMonth,
      'expYear': instance.expYear,
      'funding': _$CardFundingEnumMap[instance.funding],
      'country': instance.country,
    };

const _$CardBrandEnumMap = {
  CardBrand.visa: 'visa',
  CardBrand.mastercard: 'mastercard',
  CardBrand.amex: 'amex',
  CardBrand.discover: 'discover',
  CardBrand.jcb: 'jcb',
  CardBrand.dinersClub: 'dinersClub',
  CardBrand.unionPay: 'unionPay',
  CardBrand.unknown: 'unknown',
};

const _$CardFundingEnumMap = {
  CardFunding.credit: 'credit',
  CardFunding.debit: 'debit',
  CardFunding.prepaid: 'prepaid',
  CardFunding.unknown: 'unknown',
};

StripeCustomerModel _$StripeCustomerModelFromJson(Map<String, dynamic> json) =>
    StripeCustomerModel(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      description: json['description'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$StripeCustomerModelToJson(
        StripeCustomerModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      'phone': instance.phone,
      'description': instance.description,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
    };

WebhookEventModel _$WebhookEventModelFromJson(Map<String, dynamic> json) =>
    WebhookEventModel(
      id: json['id'] as String,
      type: json['type'] as String,
      data: json['data'] as Map<String, dynamic>,
      createdAt: DateTime.parse(json['createdAt'] as String),
      livemode: json['livemode'] as bool? ?? false,
    );

Map<String, dynamic> _$WebhookEventModelToJson(WebhookEventModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'data': instance.data,
      'createdAt': instance.createdAt.toIso8601String(),
      'livemode': instance.livemode,
    };
