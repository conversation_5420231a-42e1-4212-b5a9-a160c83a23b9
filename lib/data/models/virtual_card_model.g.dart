// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'virtual_card_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VirtualCardModel _$VirtualCardModelFromJson(Map<String, dynamic> json) =>
    VirtualCardModel(
      id: json['id'] as String,
      walletId: json['walletId'] as String,
      userId: json['userId'] as String,
      cardholderName: json['cardholderName'] as String,
      last4: json['last4'] as String,
      brand: json['brand'] as String,
      expMonth: (json['expMonth'] as num).toInt(),
      expYear: (json['expYear'] as num).toInt(),
      status: $enumDecode(_$VirtualCardStatusEnumMap, json['status']),
      type: $enumDecode(_$VirtualCardTypeEnumMap, json['type']),
      stripeCardId: json['stripeCardId'] as String?,
      stripeCardholderId: json['stripeCardholderId'] as String?,
      spendingControls: json['spendingControls'] == null
          ? const []
          : VirtualCardModel._spendingControlsFromJson(
              json['spendingControls'] as List?),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$VirtualCardModelToJson(VirtualCardModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'walletId': instance.walletId,
      'userId': instance.userId,
      'cardholderName': instance.cardholderName,
      'last4': instance.last4,
      'brand': instance.brand,
      'expMonth': instance.expMonth,
      'expYear': instance.expYear,
      'status': _$VirtualCardStatusEnumMap[instance.status]!,
      'type': _$VirtualCardTypeEnumMap[instance.type]!,
      'stripeCardId': instance.stripeCardId,
      'stripeCardholderId': instance.stripeCardholderId,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'spendingControls':
          VirtualCardModel._spendingControlsToJson(instance.spendingControls),
    };

const _$VirtualCardStatusEnumMap = {
  VirtualCardStatus.active: 'active',
  VirtualCardStatus.inactive: 'inactive',
  VirtualCardStatus.canceled: 'canceled',
  VirtualCardStatus.pending: 'pending',
};

const _$VirtualCardTypeEnumMap = {
  VirtualCardType.virtual: 'virtual',
  VirtualCardType.physical: 'physical',
};

SpendingControlModel _$SpendingControlModelFromJson(
        Map<String, dynamic> json) =>
    SpendingControlModel(
      id: json['id'] as String,
      cardId: json['cardId'] as String,
      type: $enumDecode(_$SpendingControlTypeEnumMap, json['type']),
      amount: (json['amount'] as num?)?.toDouble(),
      interval:
          $enumDecodeNullable(_$SpendingIntervalEnumMap, json['interval']),
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      allowedMerchants: (json['allowedMerchants'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      blockedMerchants: (json['blockedMerchants'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isActive: json['isActive'] as bool? ?? true,
      validFrom: json['validFrom'] == null
          ? null
          : DateTime.parse(json['validFrom'] as String),
      validUntil: json['validUntil'] == null
          ? null
          : DateTime.parse(json['validUntil'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$SpendingControlModelToJson(
        SpendingControlModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cardId': instance.cardId,
      'type': _$SpendingControlTypeEnumMap[instance.type]!,
      'amount': instance.amount,
      'interval': _$SpendingIntervalEnumMap[instance.interval],
      'categories': instance.categories,
      'allowedMerchants': instance.allowedMerchants,
      'blockedMerchants': instance.blockedMerchants,
      'isActive': instance.isActive,
      'validFrom': instance.validFrom?.toIso8601String(),
      'validUntil': instance.validUntil?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$SpendingControlTypeEnumMap = {
  SpendingControlType.amount: 'amount',
  SpendingControlType.interval: 'interval',
  SpendingControlType.category: 'category',
  SpendingControlType.merchant: 'merchant',
};

const _$SpendingIntervalEnumMap = {
  SpendingInterval.perTransaction: 'perTransaction',
  SpendingInterval.daily: 'daily',
  SpendingInterval.weekly: 'weekly',
  SpendingInterval.monthly: 'monthly',
  SpendingInterval.yearly: 'yearly',
  SpendingInterval.allTime: 'allTime',
};
