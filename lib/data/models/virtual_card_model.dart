import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/virtual_card.dart';

part 'virtual_card_model.g.dart';

@JsonSerializable()
class VirtualCardModel extends VirtualCard {
  @JsonKey(
    fromJson: _spendingControlsFromJson,
    toJson: _spendingControlsToJson,
  )
  @override
  final List<SpendingControl> spendingControls;

  const VirtualCardModel({
    required super.id,
    required super.walletId,
    required super.userId,
    required super.cardholderName,
    required super.last4,
    required super.brand,
    required super.expMonth,
    required super.expYear,
    required super.status,
    required super.type,
    super.stripeCardId,
    super.stripeCardholderId,
    this.spendingControls = const [],
    super.metadata,
    required super.createdAt,
    required super.updatedAt,
  }) : super(spendingControls: spendingControls);

  factory VirtualCardModel.fromJson(Map<String, dynamic> json) =>
      _$VirtualCardModelFromJson(json);

  Map<String, dynamic> toJson() => _$VirtualCardModelToJson(this);

  static List<SpendingControl> _spendingControlsFromJson(List<dynamic>? json) {
    if (json == null) return [];
    return json
        .map((e) =>
            SpendingControlModel.fromJson(e as Map<String, dynamic>).toEntity())
        .toList();
  }

  static List<Map<String, dynamic>> _spendingControlsToJson(
      List<SpendingControl> controls) {
    return controls
        .map((control) => SpendingControlModel.fromEntity(control).toJson())
        .toList();
  }

  factory VirtualCardModel.fromEntity(VirtualCard card) {
    return VirtualCardModel(
      id: card.id,
      walletId: card.walletId,
      userId: card.userId,
      cardholderName: card.cardholderName,
      last4: card.last4,
      brand: card.brand,
      expMonth: card.expMonth,
      expYear: card.expYear,
      status: card.status,
      type: card.type,
      stripeCardId: card.stripeCardId,
      stripeCardholderId: card.stripeCardholderId,
      spendingControls: card.spendingControls,
      metadata: card.metadata,
      createdAt: card.createdAt,
      updatedAt: card.updatedAt,
    );
  }

  VirtualCard toEntity() {
    return VirtualCard(
      id: id,
      walletId: walletId,
      userId: userId,
      cardholderName: cardholderName,
      last4: last4,
      brand: brand,
      expMonth: expMonth,
      expYear: expYear,
      status: status,
      type: type,
      stripeCardId: stripeCardId,
      stripeCardholderId: stripeCardholderId,
      spendingControls: spendingControls,
      metadata: metadata,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Create from Stripe API response
  factory VirtualCardModel.fromStripeResponse(
    Map<String, dynamic> stripeData, {
    required String walletId,
    required String userId,
  }) {
    final card = stripeData['card'] ?? stripeData;
    final cardholder = stripeData['cardholder'];

    return VirtualCardModel(
      id: card['id'],
      walletId: walletId,
      userId: userId,
      cardholderName: cardholder?['name'] ?? 'Unknown',
      last4: card['last4'],
      brand: card['brand'],
      expMonth: card['exp_month'],
      expYear: card['exp_year'],
      status: _mapStripeStatus(card['status']),
      type: _mapStripeType(card['type']),
      stripeCardId: card['id'],
      stripeCardholderId: card['cardholder'],
      spendingControls: _parseSpendingControls(card['spending_controls'] ?? []),
      metadata: card['metadata'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(card['created'] * 1000),
      updatedAt: DateTime.now(),
    );
  }

  static VirtualCardStatus _mapStripeStatus(String status) {
    switch (status) {
      case 'active':
        return VirtualCardStatus.active;
      case 'inactive':
        return VirtualCardStatus.inactive;
      case 'canceled':
        return VirtualCardStatus.canceled;
      default:
        return VirtualCardStatus.pending;
    }
  }

  static VirtualCardType _mapStripeType(String type) {
    switch (type) {
      case 'virtual':
        return VirtualCardType.virtual;
      case 'physical':
        return VirtualCardType.physical;
      default:
        return VirtualCardType.virtual;
    }
  }

  static List<SpendingControl> _parseSpendingControls(List<dynamic> controls) {
    return controls
        .map((control) =>
            SpendingControlModel.fromStripeResponse(control).toEntity())
        .toList();
  }
}

@JsonSerializable()
class SpendingControlModel extends SpendingControl {
  const SpendingControlModel({
    required super.id,
    required super.cardId,
    required super.type,
    super.amount,
    super.interval,
    super.categories,
    super.allowedMerchants,
    super.blockedMerchants,
    super.isActive,
    super.validFrom,
    super.validUntil,
    required super.createdAt,
    required super.updatedAt,
  });

  factory SpendingControlModel.fromJson(Map<String, dynamic> json) =>
      _$SpendingControlModelFromJson(json);

  Map<String, dynamic> toJson() => _$SpendingControlModelToJson(this);

  factory SpendingControlModel.fromEntity(SpendingControl control) {
    return SpendingControlModel(
      id: control.id,
      cardId: control.cardId,
      type: control.type,
      amount: control.amount,
      interval: control.interval,
      categories: control.categories,
      allowedMerchants: control.allowedMerchants,
      blockedMerchants: control.blockedMerchants,
      isActive: control.isActive,
      validFrom: control.validFrom,
      validUntil: control.validUntil,
      createdAt: control.createdAt,
      updatedAt: control.updatedAt,
    );
  }

  SpendingControl toEntity() {
    return SpendingControl(
      id: id,
      cardId: cardId,
      type: type,
      amount: amount,
      interval: interval,
      categories: categories,
      allowedMerchants: allowedMerchants,
      blockedMerchants: blockedMerchants,
      isActive: isActive,
      validFrom: validFrom,
      validUntil: validUntil,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Create from Stripe API response
  factory SpendingControlModel.fromStripeResponse(
      Map<String, dynamic> stripeData) {
    return SpendingControlModel(
      id: stripeData['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      cardId: stripeData['card_id'] ?? '',
      type: _mapStripeControlType(stripeData),
      amount: _parseAmount(stripeData['amount']),
      interval: _mapStripeInterval(stripeData['interval']),
      categories: _parseStringList(stripeData['allowed_categories']),
      allowedMerchants: _parseStringList(stripeData['allowed_merchants']),
      blockedMerchants: _parseStringList(stripeData['blocked_merchants']),
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  static SpendingControlType _mapStripeControlType(Map<String, dynamic> data) {
    if (data.containsKey('amount')) return SpendingControlType.amount;
    if (data.containsKey('allowed_categories') ||
        data.containsKey('blocked_categories')) {
      return SpendingControlType.category;
    }
    if (data.containsKey('allowed_merchants') ||
        data.containsKey('blocked_merchants')) {
      return SpendingControlType.merchant;
    }
    return SpendingControlType.amount;
  }

  static double? _parseAmount(dynamic amount) {
    if (amount == null) return null;
    if (amount is int) return amount / 100.0;
    if (amount is double) return amount;
    return null;
  }

  static SpendingInterval? _mapStripeInterval(String? interval) {
    switch (interval) {
      case 'per_authorization':
        return SpendingInterval.perTransaction;
      case 'daily':
        return SpendingInterval.daily;
      case 'weekly':
        return SpendingInterval.weekly;
      case 'monthly':
        return SpendingInterval.monthly;
      case 'yearly':
        return SpendingInterval.yearly;
      case 'all_time':
        return SpendingInterval.allTime;
      default:
        return null;
    }
  }

  static List<String>? _parseStringList(dynamic list) {
    if (list == null) return null;
    if (list is List) return list.cast<String>();
    return null;
  }
}
