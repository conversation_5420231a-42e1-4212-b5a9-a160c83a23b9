import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/payment.dart';

part 'payment_model.g.dart';

@JsonSerializable()
class PaymentIntentModel extends PaymentIntent {
  const PaymentIntentModel({
    required super.id,
    required super.amount,
    required super.currency,
    required super.status,
    required super.clientSecret,
    super.description,
    super.metadata,
    required super.createdAt,
  });

  factory PaymentIntentModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentIntentModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentIntentModelToJson(this);

  factory PaymentIntentModel.fromEntity(PaymentIntent intent) {
    return PaymentIntentModel(
      id: intent.id,
      amount: intent.amount,
      currency: intent.currency,
      status: intent.status,
      clientSecret: intent.clientSecret,
      description: intent.description,
      metadata: intent.metadata,
      createdAt: intent.createdAt,
    );
  }

  PaymentIntent toEntity() {
    return PaymentIntent(
      id: id,
      amount: amount,
      currency: currency,
      status: status,
      clientSecret: clientSecret,
      description: description,
      metadata: metadata,
      createdAt: createdAt,
    );
  }
}

@JsonSerializable()
class PaymentMethodModel extends PaymentMethod {
  @JsonKey(
    fromJson: _cardFromJson,
    toJson: _cardToJson,
  )
  @override
  final CardDetails? card;

  const PaymentMethodModel({
    required super.id,
    required super.type,
    this.card,
    super.customerId,
    required super.createdAt,
  }) : super(card: card);

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentMethodModelToJson(this);

  static CardDetails? _cardFromJson(Map<String, dynamic>? json) {
    return json != null ? CardDetailsModel.fromJson(json).toEntity() : null;
  }

  static Map<String, dynamic>? _cardToJson(CardDetails? card) {
    return card != null ? CardDetailsModel.fromEntity(card).toJson() : null;
  }

  factory PaymentMethodModel.fromEntity(PaymentMethod method) {
    return PaymentMethodModel(
      id: method.id,
      type: method.type,
      card: method.card,
      customerId: method.customerId,
      createdAt: method.createdAt,
    );
  }

  PaymentMethod toEntity() {
    return PaymentMethod(
      id: id,
      type: type,
      card: card,
      customerId: customerId,
      createdAt: createdAt,
    );
  }
}

@JsonSerializable()
class CardDetailsModel extends CardDetails {
  const CardDetailsModel({
    required super.brand,
    required super.last4,
    required super.expMonth,
    required super.expYear,
    super.funding,
    super.country,
  });

  factory CardDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$CardDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$CardDetailsModelToJson(this);

  factory CardDetailsModel.fromEntity(CardDetails card) {
    return CardDetailsModel(
      brand: card.brand,
      last4: card.last4,
      expMonth: card.expMonth,
      expYear: card.expYear,
      funding: card.funding,
      country: card.country,
    );
  }

  CardDetails toEntity() {
    return CardDetails(
      brand: brand,
      last4: last4,
      expMonth: expMonth,
      expYear: expYear,
      funding: funding,
      country: country,
    );
  }
}

@JsonSerializable()
class StripeCustomerModel extends StripeCustomer {
  const StripeCustomerModel({
    required super.id,
    required super.email,
    super.name,
    super.phone,
    super.description,
    super.metadata,
    required super.createdAt,
  });

  factory StripeCustomerModel.fromJson(Map<String, dynamic> json) =>
      _$StripeCustomerModelFromJson(json);

  Map<String, dynamic> toJson() => _$StripeCustomerModelToJson(this);

  factory StripeCustomerModel.fromEntity(StripeCustomer customer) {
    return StripeCustomerModel(
      id: customer.id,
      email: customer.email,
      name: customer.name,
      phone: customer.phone,
      description: customer.description,
      metadata: customer.metadata,
      createdAt: customer.createdAt,
    );
  }

  StripeCustomer toEntity() {
    return StripeCustomer(
      id: id,
      email: email,
      name: name,
      phone: phone,
      description: description,
      metadata: metadata,
      createdAt: createdAt,
    );
  }
}

@JsonSerializable()
class WebhookEventModel extends WebhookEvent {
  const WebhookEventModel({
    required super.id,
    required super.type,
    required super.data,
    required super.createdAt,
    super.livemode = false,
  });

  factory WebhookEventModel.fromJson(Map<String, dynamic> json) =>
      _$WebhookEventModelFromJson(json);

  Map<String, dynamic> toJson() => _$WebhookEventModelToJson(this);

  factory WebhookEventModel.fromEntity(WebhookEvent event) {
    return WebhookEventModel(
      id: event.id,
      type: event.type,
      data: event.data,
      createdAt: event.createdAt,
      livemode: event.livemode,
    );
  }

  WebhookEvent toEntity() {
    return WebhookEvent(
      id: id,
      type: type,
      data: data,
      createdAt: createdAt,
      livemode: livemode,
    );
  }
}
