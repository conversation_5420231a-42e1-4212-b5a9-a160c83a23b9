// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isEmailVerified: json['isEmailVerified'] as bool,
      isPhoneVerified: json['isPhoneVerified'] as bool,
      preferences: UserModel._preferencesFromJson(
          json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'phoneNumber': instance.phoneNumber,
      'avatarUrl': instance.avatarUrl,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isEmailVerified': instance.isEmailVerified,
      'isPhoneVerified': instance.isPhoneVerified,
      'preferences': UserModel._preferencesToJson(instance.preferences),
    };

UserPreferencesModel _$UserPreferencesModelFromJson(
        Map<String, dynamic> json) =>
    UserPreferencesModel(
      notificationsEnabled: json['notificationsEnabled'] as bool,
      emailNotificationsEnabled: json['emailNotificationsEnabled'] as bool,
      pushNotificationsEnabled: json['pushNotificationsEnabled'] as bool,
      transactionNotificationsEnabled:
          json['transactionNotificationsEnabled'] as bool,
      invitationNotificationsEnabled:
          json['invitationNotificationsEnabled'] as bool,
      currency: json['currency'] as String,
      language: json['language'] as String,
      timezone: json['timezone'] as String,
      biometricEnabled: json['biometricEnabled'] as bool,
      darkModeEnabled: json['darkModeEnabled'] as bool,
    );

Map<String, dynamic> _$UserPreferencesModelToJson(
        UserPreferencesModel instance) =>
    <String, dynamic>{
      'notificationsEnabled': instance.notificationsEnabled,
      'emailNotificationsEnabled': instance.emailNotificationsEnabled,
      'pushNotificationsEnabled': instance.pushNotificationsEnabled,
      'transactionNotificationsEnabled':
          instance.transactionNotificationsEnabled,
      'invitationNotificationsEnabled': instance.invitationNotificationsEnabled,
      'currency': instance.currency,
      'language': instance.language,
      'timezone': instance.timezone,
      'biometricEnabled': instance.biometricEnabled,
      'darkModeEnabled': instance.darkModeEnabled,
    };
