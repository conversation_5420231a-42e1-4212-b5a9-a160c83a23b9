import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:firebase_core/firebase_core.dart';

import 'core/config/app_config.dart';
import 'core/di/injection_container.dart';
import 'core/router/app_router.dart';
import 'core/theme/app_theme.dart';
import 'core/services/security_service.dart';
import 'core/services/security_audit_service.dart';
import 'core/middleware/security_middleware.dart';
import 'presentation/blocs/auth/auth_bloc.dart';
import 'presentation/blocs/app/app_bloc.dart';
import 'presentation/blocs/wallet/wallet_bloc.dart';
import 'presentation/blocs/payment/payment_bloc.dart';
import 'presentation/blocs/card/card_bloc.dart';
import 'presentation/blocs/virtual_card/virtual_card_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Load environment variables
    await dotenv.load(fileName: ".env");

    // Initialize Firebase
    await Firebase.initializeApp();

    // Initialize Supabase
    await Supabase.initialize(
      url: AppConfig.supabaseUrl,
      anonKey: AppConfig.supabaseAnonKey,
    );

    // Initialize security services
    await SecurityService.initialize();

    // Initialize dependency injection
    await initializeDependencies();

    // Initialize security middleware
    final auditService = getIt<SecurityAuditService>();
    await SecurityMiddleware.initialize(auditService);

    runApp(const PottoApp());
  } catch (e) {
    // Log initialization error
    debugPrint('App initialization failed: $e');

    // Run app with minimal functionality
    runApp(const PottoApp());
  }
}

class PottoApp extends StatelessWidget {
  const PottoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AppBloc>(
          create: (context) => getIt<AppBloc>()..add(const AppStarted()),
        ),
        BlocProvider<AuthBloc>(
          create: (context) => getIt<AuthBloc>(),
        ),
        BlocProvider<WalletBloc>(
          create: (context) => getIt<WalletBloc>(),
        ),
        BlocProvider<PaymentBloc>(
          create: (context) => getIt<PaymentBloc>(),
        ),
        BlocProvider<CardBloc>(
          create: (context) => getIt<CardBloc>(),
        ),
        BlocProvider<VirtualCardBloc>(
          create: (context) => getIt<VirtualCardBloc>(),
        ),
      ],
      child: BlocBuilder<AppBloc, AppState>(
        builder: (context, appState) {
          return MaterialApp.router(
            title: 'Potto',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: appState.themeMode,
            routerConfig: AppRouter.router,
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: TextScaler.linear(
                    MediaQuery.of(context)
                        .textScaler
                        .scale(1.0)
                        .clamp(0.8, 1.2),
                  ),
                ),
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
