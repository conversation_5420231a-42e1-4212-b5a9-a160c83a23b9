import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/invitation.dart';
import '../../entities/wallet.dart';
import '../../repositories/wallet_repository.dart';

class CreateInvitationUseCase
    implements UseCase<Invitation, CreateInvitationParams> {
  final WalletRepository repository;

  CreateInvitationUseCase(this.repository);

  @override
  Future<Either<Failure, Invitation>> call(
      CreateInvitationParams params) async {
    return await repository.createInvitation(params.request);
  }
}

class CreateInvitationParams {
  final CreateInvitationRequest request;

  CreateInvitationParams({required this.request});
}

class CreateInvitationRequest {
  final String walletId;
  final String email;
  final DateTime? expiresAt;

  CreateInvitationRequest({
    required this.walletId,
    required this.email,
    this.expiresAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'wallet_id': walletId,
      'email': email,
      'expires_at': (expiresAt ?? DateTime.now().add(const Duration(days: 7)))
          .toIso8601String(),
    };
  }
}

class GetInvitationsUseCase
    implements UseCase<List<Invitation>, GetInvitationsParams> {
  final WalletRepository repository;

  GetInvitationsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Invitation>>> call(
      GetInvitationsParams params) async {
    return await repository.getWalletInvitations(params.walletId);
  }
}

class GetInvitationsParams {
  final String walletId;

  GetInvitationsParams({required this.walletId});
}

class AcceptInvitationUseCase
    implements UseCase<Wallet, AcceptInvitationParams> {
  final WalletRepository repository;

  AcceptInvitationUseCase(this.repository);

  @override
  Future<Either<Failure, Wallet>> call(AcceptInvitationParams params) async {
    return await repository.acceptInvitation(params.inviteCode);
  }
}

class AcceptInvitationParams {
  final String inviteCode;

  AcceptInvitationParams({required this.inviteCode});
}

class CancelInvitationUseCase implements UseCase<void, CancelInvitationParams> {
  final WalletRepository repository;

  CancelInvitationUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(CancelInvitationParams params) async {
    return await repository.cancelInvitation(params.invitationId);
  }
}

class CancelInvitationParams {
  final String invitationId;

  CancelInvitationParams({required this.invitationId});
}
