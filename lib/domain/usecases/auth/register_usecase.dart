import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/user.dart';
import '../../repositories/auth_repository.dart';

class RegisterUseCase implements UseCase<User, RegisterParams> {
  final AuthRepository repository;

  RegisterUseCase(this.repository);

  @override
  Future<Either<Failure, User>> call(RegisterParams params) async {
    return await repository.register(
      params.email,
      params.password,
      firstName: params.firstName,
      lastName: params.lastName,
    );
  }
}

class RegisterParams {
  final String email;
  final String password;
  final String? firstName;
  final String? lastName;

  RegisterParams({
    required this.email,
    required this.password,
    this.firstName,
    this.lastName,
  });
}
