// Mocks generated by Mockito 5.4.5 from annotations
// in potto_app/test/test_utils/mocks.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:dio/dio.dart' as _i9;
import 'package:dio/src/adapter.dart' as _i5;
import 'package:dio/src/cancel_token.dart' as _i16;
import 'package:dio/src/dio_mixin.dart' as _i7;
import 'package:dio/src/options.dart' as _i4;
import 'package:dio/src/response.dart' as _i8;
import 'package:dio/src/transformer.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i14;
import 'package:potto_app/core/network/api_client.dart' as _i24;
import 'package:potto_app/core/services/chat_service.dart' as _i11;
import 'package:potto_app/core/services/fraud_prevention_service.dart' as _i12;
import 'package:potto_app/core/services/notification_service.dart' as _i21;
import 'package:potto_app/core/services/realtime_service.dart' as _i22;
import 'package:potto_app/core/services/security_audit_service.dart' as _i13;
import 'package:potto_app/core/services/storage_service.dart' as _i17;
import 'package:potto_app/core/services/stripe_issuing_service.dart' as _i19;
import 'package:potto_app/core/services/stripe_service.dart' as _i18;
import 'package:potto_app/core/services/webhook_service.dart' as _i23;
import 'package:potto_app/domain/entities/payment.dart' as _i10;
import 'package:potto_app/domain/entities/virtual_card.dart' as _i20;
import 'package:shared_preferences/shared_preferences.dart' as _i15;
import 'package:supabase/supabase.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFunctionsClient_0 extends _i1.SmartFake
    implements _i2.FunctionsClient {
  _FakeFunctionsClient_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseStorageClient_1 extends _i1.SmartFake
    implements _i2.SupabaseStorageClient {
  _FakeSupabaseStorageClient_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimeClient_2 extends _i1.SmartFake
    implements _i2.RealtimeClient {
  _FakeRealtimeClient_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestClient_3 extends _i1.SmartFake
    implements _i2.PostgrestClient {
  _FakePostgrestClient_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoTrueClient_4 extends _i1.SmartFake implements _i2.GoTrueClient {
  _FakeGoTrueClient_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseQueryBuilder_5 extends _i1.SmartFake
    implements _i2.SupabaseQueryBuilder {
  _FakeSupabaseQueryBuilder_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseQuerySchema_6 extends _i1.SmartFake
    implements _i2.SupabaseQuerySchema {
  _FakeSupabaseQuerySchema_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestFilterBuilder_7<T1> extends _i1.SmartFake
    implements _i2.PostgrestFilterBuilder<T1> {
  _FakePostgrestFilterBuilder_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimeChannel_8 extends _i1.SmartFake
    implements _i2.RealtimeChannel {
  _FakeRealtimeChannel_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestTransformBuilder_9<T1> extends _i1.SmartFake
    implements _i2.PostgrestTransformBuilder<T1> {
  _FakePostgrestTransformBuilder_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeResponsePostgrestBuilder_10<T1, S, R> extends _i1.SmartFake
    implements _i2.ResponsePostgrestBuilder<T1, S, R> {
  _FakeResponsePostgrestBuilder_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestBuilder_11<T1, S, R> extends _i1.SmartFake
    implements _i2.PostgrestBuilder<T1, S, R> {
  _FakePostgrestBuilder_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUri_12 extends _i1.SmartFake implements Uri {
  _FakeUri_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFuture_13<T1> extends _i1.SmartFake implements _i3.Future<T1> {
  _FakeFuture_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaseOptions_14 extends _i1.SmartFake implements _i4.BaseOptions {
  _FakeBaseOptions_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeHttpClientAdapter_15 extends _i1.SmartFake
    implements _i5.HttpClientAdapter {
  _FakeHttpClientAdapter_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTransformer_16 extends _i1.SmartFake implements _i6.Transformer {
  _FakeTransformer_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInterceptors_17 extends _i1.SmartFake implements _i7.Interceptors {
  _FakeInterceptors_17(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeResponse_18<T1> extends _i1.SmartFake implements _i8.Response<T1> {
  _FakeResponse_18(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDio_19 extends _i1.SmartFake implements _i9.Dio {
  _FakeDio_19(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePaymentIntent_20 extends _i1.SmartFake
    implements _i10.PaymentIntent {
  _FakePaymentIntent_20(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStripeCustomer_21 extends _i1.SmartFake
    implements _i10.StripeCustomer {
  _FakeStripeCustomer_21(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeChatMessage_22 extends _i1.SmartFake implements _i11.ChatMessage {
  _FakeChatMessage_22(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRiskAssessment_23 extends _i1.SmartFake
    implements _i12.RiskAssessment {
  _FakeRiskAssessment_23(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeComplianceStatus_24 extends _i1.SmartFake
    implements _i13.ComplianceStatus {
  _FakeComplianceStatus_24(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SupabaseClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseClient extends _i1.Mock implements _i2.SupabaseClient {
  MockSupabaseClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FunctionsClient get functions => (super.noSuchMethod(
        Invocation.getter(#functions),
        returnValue: _FakeFunctionsClient_0(
          this,
          Invocation.getter(#functions),
        ),
      ) as _i2.FunctionsClient);

  @override
  set functions(_i2.FunctionsClient? _functions) => super.noSuchMethod(
        Invocation.setter(
          #functions,
          _functions,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.SupabaseStorageClient get storage => (super.noSuchMethod(
        Invocation.getter(#storage),
        returnValue: _FakeSupabaseStorageClient_1(
          this,
          Invocation.getter(#storage),
        ),
      ) as _i2.SupabaseStorageClient);

  @override
  set storage(_i2.SupabaseStorageClient? _storage) => super.noSuchMethod(
        Invocation.setter(
          #storage,
          _storage,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.RealtimeClient get realtime => (super.noSuchMethod(
        Invocation.getter(#realtime),
        returnValue: _FakeRealtimeClient_2(
          this,
          Invocation.getter(#realtime),
        ),
      ) as _i2.RealtimeClient);

  @override
  set realtime(_i2.RealtimeClient? _realtime) => super.noSuchMethod(
        Invocation.setter(
          #realtime,
          _realtime,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.PostgrestClient get rest => (super.noSuchMethod(
        Invocation.getter(#rest),
        returnValue: _FakePostgrestClient_3(
          this,
          Invocation.getter(#rest),
        ),
      ) as _i2.PostgrestClient);

  @override
  set rest(_i2.PostgrestClient? _rest) => super.noSuchMethod(
        Invocation.setter(
          #rest,
          _rest,
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, String> get headers => (super.noSuchMethod(
        Invocation.getter(#headers),
        returnValue: <String, String>{},
      ) as Map<String, String>);

  @override
  set headers(Map<String, String>? headers) => super.noSuchMethod(
        Invocation.setter(
          #headers,
          headers,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.GoTrueClient get auth => (super.noSuchMethod(
        Invocation.getter(#auth),
        returnValue: _FakeGoTrueClient_4(
          this,
          Invocation.getter(#auth),
        ),
      ) as _i2.GoTrueClient);

  @override
  _i2.SupabaseQueryBuilder from(String? table) => (super.noSuchMethod(
        Invocation.method(
          #from,
          [table],
        ),
        returnValue: _FakeSupabaseQueryBuilder_5(
          this,
          Invocation.method(
            #from,
            [table],
          ),
        ),
      ) as _i2.SupabaseQueryBuilder);

  @override
  _i2.SupabaseQuerySchema schema(String? schema) => (super.noSuchMethod(
        Invocation.method(
          #schema,
          [schema],
        ),
        returnValue: _FakeSupabaseQuerySchema_6(
          this,
          Invocation.method(
            #schema,
            [schema],
          ),
        ),
      ) as _i2.SupabaseQuerySchema);

  @override
  _i2.PostgrestFilterBuilder<T> rpc<T>(
    String? fn, {
    Map<String, dynamic>? params,
    dynamic get = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #rpc,
          [fn],
          {
            #params: params,
            #get: get,
          },
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rpc,
            [fn],
            {
              #params: params,
              #get: get,
            },
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.RealtimeChannel channel(
    String? name, {
    _i2.RealtimeChannelConfig? opts = const _i2.RealtimeChannelConfig(),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #channel,
          [name],
          {#opts: opts},
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #channel,
            [name],
            {#opts: opts},
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  List<_i2.RealtimeChannel> getChannels() => (super.noSuchMethod(
        Invocation.method(
          #getChannels,
          [],
        ),
        returnValue: <_i2.RealtimeChannel>[],
      ) as List<_i2.RealtimeChannel>);

  @override
  _i3.Future<String> removeChannel(_i2.RealtimeChannel? channel) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeChannel,
          [channel],
        ),
        returnValue: _i3.Future<String>.value(_i14.dummyValue<String>(
          this,
          Invocation.method(
            #removeChannel,
            [channel],
          ),
        )),
      ) as _i3.Future<String>);

  @override
  _i3.Future<List<String>> removeAllChannels() => (super.noSuchMethod(
        Invocation.method(
          #removeAllChannels,
          [],
        ),
        returnValue: _i3.Future<List<String>>.value(<String>[]),
      ) as _i3.Future<List<String>>);

  @override
  _i3.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

/// A class which mocks [PostgrestFilterBuilder].
///
/// See the documentation for Mockito's code generation for more information.
class MockPostgrestFilterBuilder<T> extends _i1.Mock
    implements _i2.PostgrestFilterBuilder<T> {
  MockPostgrestFilterBuilder() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PostgrestFilterBuilder<T> copyWithUrl(Uri? url) => (super.noSuchMethod(
        Invocation.method(
          #copyWithUrl,
          [url],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #copyWithUrl,
            [url],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> not(
    String? column,
    String? operator,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #not,
          [
            column,
            operator,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #not,
            [
              column,
              operator,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> or(
    String? filters, {
    String? referencedTable,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #or,
          [filters],
          {#referencedTable: referencedTable},
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #or,
            [filters],
            {#referencedTable: referencedTable},
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> eq(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #eq,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #eq,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> neq(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #neq,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #neq,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> gt(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #gt,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #gt,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> gte(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #gte,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #gte,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> lt(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #lt,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #lt,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> lte(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #lte,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #lte,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> like(
    String? column,
    String? pattern,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #like,
          [
            column,
            pattern,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #like,
            [
              column,
              pattern,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> likeAllOf(
    String? column,
    List<String>? patterns,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #likeAllOf,
          [
            column,
            patterns,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
          this,
          Invocation.method(
            #likeAllOf,
            [
              column,
              patterns,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> likeAnyOf(
    String? column,
    List<String>? patterns,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #likeAnyOf,
          [
            column,
            patterns,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
          this,
          Invocation.method(
            #likeAnyOf,
            [
              column,
              patterns,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<T> ilike(
    String? column,
    String? pattern,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #ilike,
          [
            column,
            pattern,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #ilike,
            [
              column,
              pattern,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> ilikeAllOf(
    String? column,
    List<String>? patterns,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #ilikeAllOf,
          [
            column,
            patterns,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
          this,
          Invocation.method(
            #ilikeAllOf,
            [
              column,
              patterns,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> ilikeAnyOf(
    String? column,
    List<String>? patterns,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #ilikeAnyOf,
          [
            column,
            patterns,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
          this,
          Invocation.method(
            #ilikeAnyOf,
            [
              column,
              patterns,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<T> isFilter(
    String? column,
    bool? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #isFilter,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #isFilter,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> inFilter(
    String? column,
    List<dynamic>? values,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #inFilter,
          [
            column,
            values,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #inFilter,
            [
              column,
              values,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> contains(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #contains,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #contains,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> containedBy(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #containedBy,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #containedBy,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeLt(
    String? column,
    String? range,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rangeLt,
          [
            column,
            range,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rangeLt,
            [
              column,
              range,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeGt(
    String? column,
    String? range,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rangeGt,
          [
            column,
            range,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rangeGt,
            [
              column,
              range,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeGte(
    String? column,
    String? range,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rangeGte,
          [
            column,
            range,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rangeGte,
            [
              column,
              range,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeLte(
    String? column,
    String? range,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rangeLte,
          [
            column,
            range,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rangeLte,
            [
              column,
              range,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeAdjacent(
    String? column,
    String? range,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rangeAdjacent,
          [
            column,
            range,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rangeAdjacent,
            [
              column,
              range,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> overlaps(
    String? column,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #overlaps,
          [
            column,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #overlaps,
            [
              column,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> textSearch(
    String? column,
    String? query, {
    String? config,
    _i2.TextSearchType? type,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #textSearch,
          [
            column,
            query,
          ],
          {
            #config: config,
            #type: type,
          },
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #textSearch,
            [
              column,
              query,
            ],
            {
              #config: config,
              #type: type,
            },
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> filter(
    String? column,
    String? operator,
    Object? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #filter,
          [
            column,
            operator,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #filter,
            [
              column,
              operator,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> match(Map<String, Object>? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #match,
          [query],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #match,
            [query],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> setHeader(
    String? key,
    String? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setHeader,
          [
            key,
            value,
          ],
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #setHeader,
            [
              key,
              value,
            ],
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestTransformBuilder<List<Map<String, dynamic>>> select(
          [String? columns = '*']) =>
      (super.noSuchMethod(
        Invocation.method(
          #select,
          [columns],
        ),
        returnValue:
            _FakePostgrestTransformBuilder_9<List<Map<String, dynamic>>>(
          this,
          Invocation.method(
            #select,
            [columns],
          ),
        ),
      ) as _i2.PostgrestTransformBuilder<List<Map<String, dynamic>>>);

  @override
  _i2.PostgrestTransformBuilder<T> order(
    String? column, {
    bool? ascending = false,
    bool? nullsFirst = false,
    String? referencedTable,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #order,
          [column],
          {
            #ascending: ascending,
            #nullsFirst: nullsFirst,
            #referencedTable: referencedTable,
          },
        ),
        returnValue: _FakePostgrestTransformBuilder_9<T>(
          this,
          Invocation.method(
            #order,
            [column],
            {
              #ascending: ascending,
              #nullsFirst: nullsFirst,
              #referencedTable: referencedTable,
            },
          ),
        ),
      ) as _i2.PostgrestTransformBuilder<T>);

  @override
  _i2.PostgrestTransformBuilder<T> limit(
    int? count, {
    String? referencedTable,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #limit,
          [count],
          {#referencedTable: referencedTable},
        ),
        returnValue: _FakePostgrestTransformBuilder_9<T>(
          this,
          Invocation.method(
            #limit,
            [count],
            {#referencedTable: referencedTable},
          ),
        ),
      ) as _i2.PostgrestTransformBuilder<T>);

  @override
  _i2.PostgrestTransformBuilder<T> range(
    int? from,
    int? to, {
    String? referencedTable,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #range,
          [
            from,
            to,
          ],
          {#referencedTable: referencedTable},
        ),
        returnValue: _FakePostgrestTransformBuilder_9<T>(
          this,
          Invocation.method(
            #range,
            [
              from,
              to,
            ],
            {#referencedTable: referencedTable},
          ),
        ),
      ) as _i2.PostgrestTransformBuilder<T>);

  @override
  _i2.PostgrestTransformBuilder<Map<String, dynamic>> single() =>
      (super.noSuchMethod(
        Invocation.method(
          #single,
          [],
        ),
        returnValue: _FakePostgrestTransformBuilder_9<Map<String, dynamic>>(
          this,
          Invocation.method(
            #single,
            [],
          ),
        ),
      ) as _i2.PostgrestTransformBuilder<Map<String, dynamic>>);

  @override
  _i2.PostgrestTransformBuilder<Map<String, dynamic>?> maybeSingle() =>
      (super.noSuchMethod(
        Invocation.method(
          #maybeSingle,
          [],
        ),
        returnValue: _FakePostgrestTransformBuilder_9<Map<String, dynamic>?>(
          this,
          Invocation.method(
            #maybeSingle,
            [],
          ),
        ),
      ) as _i2.PostgrestTransformBuilder<Map<String, dynamic>?>);

  @override
  _i2.PostgrestTransformBuilder<String> csv() => (super.noSuchMethod(
        Invocation.method(
          #csv,
          [],
        ),
        returnValue: _FakePostgrestTransformBuilder_9<String>(
          this,
          Invocation.method(
            #csv,
            [],
          ),
        ),
      ) as _i2.PostgrestTransformBuilder<String>);

  @override
  _i2.ResponsePostgrestBuilder<_i2.PostgrestResponse<T>, T, T> count(
          [_i2.CountOption? count = _i2.CountOption.exact]) =>
      (super.noSuchMethod(
        Invocation.method(
          #count,
          [count],
        ),
        returnValue:
            _FakeResponsePostgrestBuilder_10<_i2.PostgrestResponse<T>, T, T>(
          this,
          Invocation.method(
            #count,
            [count],
          ),
        ),
      ) as _i2.ResponsePostgrestBuilder<_i2.PostgrestResponse<T>, T, T>);

  @override
  _i2.PostgrestBuilder<void, void, void> head() => (super.noSuchMethod(
        Invocation.method(
          #head,
          [],
        ),
        returnValue: _FakePostgrestBuilder_11<void, void, void>(
          this,
          Invocation.method(
            #head,
            [],
          ),
        ),
      ) as _i2.PostgrestBuilder<void, void, void>);

  @override
  _i2.ResponsePostgrestBuilder<Map<String, dynamic>,
          Map<String, dynamic>, Map<String, dynamic>>
      geojson() => (super.noSuchMethod(
            Invocation.method(
              #geojson,
              [],
            ),
            returnValue: _FakeResponsePostgrestBuilder_10<Map<String, dynamic>,
                Map<String, dynamic>, Map<String, dynamic>>(
              this,
              Invocation.method(
                #geojson,
                [],
              ),
            ),
          ) as _i2.ResponsePostgrestBuilder<Map<String, dynamic>,
              Map<String, dynamic>, Map<String, dynamic>>);

  @override
  _i2.PostgrestBuilder<String, String, String> explain({
    bool? analyze = false,
    bool? verbose = false,
    bool? settings = false,
    bool? buffers = false,
    bool? wal = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #explain,
          [],
          {
            #analyze: analyze,
            #verbose: verbose,
            #settings: settings,
            #buffers: buffers,
            #wal: wal,
          },
        ),
        returnValue: _FakePostgrestBuilder_11<String, String, String>(
          this,
          Invocation.method(
            #explain,
            [],
            {
              #analyze: analyze,
              #verbose: verbose,
              #settings: settings,
              #buffers: buffers,
              #wal: wal,
            },
          ),
        ),
      ) as _i2.PostgrestBuilder<String, String, String>);

  @override
  _i2.PostgrestBuilder<U, U, T> withConverter<U>(
          _i2.PostgrestConverter<U, T>? converter) =>
      (super.noSuchMethod(
        Invocation.method(
          #withConverter,
          [converter],
        ),
        returnValue: _FakePostgrestBuilder_11<U, U, T>(
          this,
          Invocation.method(
            #withConverter,
            [converter],
          ),
        ),
      ) as _i2.PostgrestBuilder<U, U, T>);

  @override
  Uri appendSearchParams(
    String? key,
    String? value, [
    Uri? url,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #appendSearchParams,
          [
            key,
            value,
            url,
          ],
        ),
        returnValue: _FakeUri_12(
          this,
          Invocation.method(
            #appendSearchParams,
            [
              key,
              value,
              url,
            ],
          ),
        ),
      ) as Uri);

  @override
  Uri overrideSearchParams(
    String? key,
    String? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #overrideSearchParams,
          [
            key,
            value,
          ],
        ),
        returnValue: _FakeUri_12(
          this,
          Invocation.method(
            #overrideSearchParams,
            [
              key,
              value,
            ],
          ),
        ),
      ) as Uri);

  @override
  _i3.Stream<T> asStream() => (super.noSuchMethod(
        Invocation.method(
          #asStream,
          [],
        ),
        returnValue: _i3.Stream<T>.empty(),
      ) as _i3.Stream<T>);

  @override
  _i3.Future<T> catchError(
    Function? onError, {
    bool Function(Object)? test,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #catchError,
          [onError],
          {#test: test},
        ),
        returnValue: _i14.ifNotNull(
              _i14.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #catchError,
                  [onError],
                  {#test: test},
                ),
              ),
              (T v) => _i3.Future<T>.value(v),
            ) ??
            _FakeFuture_13<T>(
              this,
              Invocation.method(
                #catchError,
                [onError],
                {#test: test},
              ),
            ),
      ) as _i3.Future<T>);

  @override
  _i3.Future<U> then<U>(
    _i3.FutureOr<U> Function(T)? onValue, {
    Function? onError,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #then,
          [onValue],
          {#onError: onError},
        ),
        returnValue: _i14.ifNotNull(
              _i14.dummyValueOrNull<U>(
                this,
                Invocation.method(
                  #then,
                  [onValue],
                  {#onError: onError},
                ),
              ),
              (U v) => _i3.Future<U>.value(v),
            ) ??
            _FakeFuture_13<U>(
              this,
              Invocation.method(
                #then,
                [onValue],
                {#onError: onError},
              ),
            ),
      ) as _i3.Future<U>);

  @override
  _i3.Future<T> timeout(
    Duration? timeLimit, {
    _i3.FutureOr<T> Function()? onTimeout,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #timeout,
          [timeLimit],
          {#onTimeout: onTimeout},
        ),
        returnValue: _i14.ifNotNull(
              _i14.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #timeout,
                  [timeLimit],
                  {#onTimeout: onTimeout},
                ),
              ),
              (T v) => _i3.Future<T>.value(v),
            ) ??
            _FakeFuture_13<T>(
              this,
              Invocation.method(
                #timeout,
                [timeLimit],
                {#onTimeout: onTimeout},
              ),
            ),
      ) as _i3.Future<T>);

  @override
  _i3.Future<T> whenComplete(_i3.FutureOr<void> Function()? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #whenComplete,
          [action],
        ),
        returnValue: _i14.ifNotNull(
              _i14.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #whenComplete,
                  [action],
                ),
              ),
              (T v) => _i3.Future<T>.value(v),
            ) ??
            _FakeFuture_13<T>(
              this,
              Invocation.method(
                #whenComplete,
                [action],
              ),
            ),
      ) as _i3.Future<T>);
}

/// A class which mocks [SharedPreferences].
///
/// See the documentation for Mockito's code generation for more information.
class MockSharedPreferences extends _i1.Mock implements _i15.SharedPreferences {
  MockSharedPreferences() {
    _i1.throwOnMissingStub(this);
  }

  @override
  Set<String> getKeys() => (super.noSuchMethod(
        Invocation.method(
          #getKeys,
          [],
        ),
        returnValue: <String>{},
      ) as Set<String>);

  @override
  Object? get(String? key) => (super.noSuchMethod(Invocation.method(
        #get,
        [key],
      )) as Object?);

  @override
  bool? getBool(String? key) => (super.noSuchMethod(Invocation.method(
        #getBool,
        [key],
      )) as bool?);

  @override
  int? getInt(String? key) => (super.noSuchMethod(Invocation.method(
        #getInt,
        [key],
      )) as int?);

  @override
  double? getDouble(String? key) => (super.noSuchMethod(Invocation.method(
        #getDouble,
        [key],
      )) as double?);

  @override
  String? getString(String? key) => (super.noSuchMethod(Invocation.method(
        #getString,
        [key],
      )) as String?);

  @override
  bool containsKey(String? key) => (super.noSuchMethod(
        Invocation.method(
          #containsKey,
          [key],
        ),
        returnValue: false,
      ) as bool);

  @override
  List<String>? getStringList(String? key) =>
      (super.noSuchMethod(Invocation.method(
        #getStringList,
        [key],
      )) as List<String>?);

  @override
  _i3.Future<bool> setBool(
    String? key,
    bool? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setBool,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setInt(
    String? key,
    int? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setInt,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setDouble(
    String? key,
    double? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setDouble,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setString(
    String? key,
    String? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setString,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setStringList(
    String? key,
    List<String>? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setStringList,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> remove(String? key) => (super.noSuchMethod(
        Invocation.method(
          #remove,
          [key],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> commit() => (super.noSuchMethod(
        Invocation.method(
          #commit,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> clear() => (super.noSuchMethod(
        Invocation.method(
          #clear,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> reload() => (super.noSuchMethod(
        Invocation.method(
          #reload,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

/// A class which mocks [Dio].
///
/// See the documentation for Mockito's code generation for more information.
class MockDio extends _i1.Mock implements _i9.Dio {
  MockDio() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.BaseOptions get options => (super.noSuchMethod(
        Invocation.getter(#options),
        returnValue: _FakeBaseOptions_14(
          this,
          Invocation.getter(#options),
        ),
      ) as _i4.BaseOptions);

  @override
  set options(_i4.BaseOptions? _options) => super.noSuchMethod(
        Invocation.setter(
          #options,
          _options,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.HttpClientAdapter get httpClientAdapter => (super.noSuchMethod(
        Invocation.getter(#httpClientAdapter),
        returnValue: _FakeHttpClientAdapter_15(
          this,
          Invocation.getter(#httpClientAdapter),
        ),
      ) as _i5.HttpClientAdapter);

  @override
  set httpClientAdapter(_i5.HttpClientAdapter? _httpClientAdapter) =>
      super.noSuchMethod(
        Invocation.setter(
          #httpClientAdapter,
          _httpClientAdapter,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Transformer get transformer => (super.noSuchMethod(
        Invocation.getter(#transformer),
        returnValue: _FakeTransformer_16(
          this,
          Invocation.getter(#transformer),
        ),
      ) as _i6.Transformer);

  @override
  set transformer(_i6.Transformer? _transformer) => super.noSuchMethod(
        Invocation.setter(
          #transformer,
          _transformer,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Interceptors get interceptors => (super.noSuchMethod(
        Invocation.getter(#interceptors),
        returnValue: _FakeInterceptors_17(
          this,
          Invocation.getter(#interceptors),
        ),
      ) as _i7.Interceptors);

  @override
  void close({bool? force = false}) => super.noSuchMethod(
        Invocation.method(
          #close,
          [],
          {#force: force},
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i3.Future<_i8.Response<T>> head<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #head,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #head,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> headUri<T>(
    Uri? uri, {
    Object? data,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #headUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #headUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> get<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #get,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> getUri<T>(
    Uri? uri, {
    Object? data,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #getUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> post<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
    _i4.ProgressCallback? onSendProgress,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #post,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #post,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> postUri<T>(
    Uri? uri, {
    Object? data,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
    _i4.ProgressCallback? onSendProgress,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #postUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #postUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> put<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
    _i4.ProgressCallback? onSendProgress,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #put,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #put,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> putUri<T>(
    Uri? uri, {
    Object? data,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
    _i4.ProgressCallback? onSendProgress,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #putUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #putUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> patch<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
    _i4.ProgressCallback? onSendProgress,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #patch,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #patch,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> patchUri<T>(
    Uri? uri, {
    Object? data,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
    _i4.ProgressCallback? onSendProgress,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #patchUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #patchUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> delete<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [path],
          {
            #data: data,
            #queryParameters: queryParameters,
            #options: options,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #delete,
            [path],
            {
              #data: data,
              #queryParameters: queryParameters,
              #options: options,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> deleteUri<T>(
    Uri? uri, {
    Object? data,
    _i4.Options? options,
    _i16.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteUri,
          [uri],
          {
            #data: data,
            #options: options,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #deleteUri,
            [uri],
            {
              #data: data,
              #options: options,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<dynamic>> download(
    String? urlPath,
    dynamic savePath, {
    _i4.ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    _i16.CancelToken? cancelToken,
    bool? deleteOnError = true,
    _i4.FileAccessMode? fileAccessMode = _i4.FileAccessMode.write,
    String? lengthHeader = 'content-length',
    Object? data,
    _i4.Options? options,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #download,
          [
            urlPath,
            savePath,
          ],
          {
            #onReceiveProgress: onReceiveProgress,
            #queryParameters: queryParameters,
            #cancelToken: cancelToken,
            #deleteOnError: deleteOnError,
            #fileAccessMode: fileAccessMode,
            #lengthHeader: lengthHeader,
            #data: data,
            #options: options,
          },
        ),
        returnValue:
            _i3.Future<_i8.Response<dynamic>>.value(_FakeResponse_18<dynamic>(
          this,
          Invocation.method(
            #download,
            [
              urlPath,
              savePath,
            ],
            {
              #onReceiveProgress: onReceiveProgress,
              #queryParameters: queryParameters,
              #cancelToken: cancelToken,
              #deleteOnError: deleteOnError,
              #fileAccessMode: fileAccessMode,
              #lengthHeader: lengthHeader,
              #data: data,
              #options: options,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<dynamic>>);

  @override
  _i3.Future<_i8.Response<dynamic>> downloadUri(
    Uri? uri,
    dynamic savePath, {
    _i4.ProgressCallback? onReceiveProgress,
    _i16.CancelToken? cancelToken,
    bool? deleteOnError = true,
    _i4.FileAccessMode? fileAccessMode = _i4.FileAccessMode.write,
    String? lengthHeader = 'content-length',
    Object? data,
    _i4.Options? options,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadUri,
          [
            uri,
            savePath,
          ],
          {
            #onReceiveProgress: onReceiveProgress,
            #cancelToken: cancelToken,
            #deleteOnError: deleteOnError,
            #fileAccessMode: fileAccessMode,
            #lengthHeader: lengthHeader,
            #data: data,
            #options: options,
          },
        ),
        returnValue:
            _i3.Future<_i8.Response<dynamic>>.value(_FakeResponse_18<dynamic>(
          this,
          Invocation.method(
            #downloadUri,
            [
              uri,
              savePath,
            ],
            {
              #onReceiveProgress: onReceiveProgress,
              #cancelToken: cancelToken,
              #deleteOnError: deleteOnError,
              #fileAccessMode: fileAccessMode,
              #lengthHeader: lengthHeader,
              #data: data,
              #options: options,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<dynamic>>);

  @override
  _i3.Future<_i8.Response<T>> request<T>(
    String? url, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i16.CancelToken? cancelToken,
    _i4.Options? options,
    _i4.ProgressCallback? onSendProgress,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #request,
          [url],
          {
            #data: data,
            #queryParameters: queryParameters,
            #cancelToken: cancelToken,
            #options: options,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #request,
            [url],
            {
              #data: data,
              #queryParameters: queryParameters,
              #cancelToken: cancelToken,
              #options: options,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> requestUri<T>(
    Uri? uri, {
    Object? data,
    _i16.CancelToken? cancelToken,
    _i4.Options? options,
    _i4.ProgressCallback? onSendProgress,
    _i4.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestUri,
          [uri],
          {
            #data: data,
            #cancelToken: cancelToken,
            #options: options,
            #onSendProgress: onSendProgress,
            #onReceiveProgress: onReceiveProgress,
          },
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #requestUri,
            [uri],
            {
              #data: data,
              #cancelToken: cancelToken,
              #options: options,
              #onSendProgress: onSendProgress,
              #onReceiveProgress: onReceiveProgress,
            },
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i3.Future<_i8.Response<T>> fetch<T>(_i4.RequestOptions? requestOptions) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetch,
          [requestOptions],
        ),
        returnValue: _i3.Future<_i8.Response<T>>.value(_FakeResponse_18<T>(
          this,
          Invocation.method(
            #fetch,
            [requestOptions],
          ),
        )),
      ) as _i3.Future<_i8.Response<T>>);

  @override
  _i9.Dio clone({
    _i4.BaseOptions? options,
    _i7.Interceptors? interceptors,
    _i5.HttpClientAdapter? httpClientAdapter,
    _i6.Transformer? transformer,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #clone,
          [],
          {
            #options: options,
            #interceptors: interceptors,
            #httpClientAdapter: httpClientAdapter,
            #transformer: transformer,
          },
        ),
        returnValue: _FakeDio_19(
          this,
          Invocation.method(
            #clone,
            [],
            {
              #options: options,
              #interceptors: interceptors,
              #httpClientAdapter: httpClientAdapter,
              #transformer: transformer,
            },
          ),
        ),
      ) as _i9.Dio);
}

/// A class which mocks [StorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockStorageService extends _i1.Mock implements _i17.StorageService {
  MockStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<bool> setString(
    String? key,
    String? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setString,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  String? getString(String? key) => (super.noSuchMethod(Invocation.method(
        #getString,
        [key],
      )) as String?);

  @override
  _i3.Future<bool> setInt(
    String? key,
    int? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setInt,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  int? getInt(String? key) => (super.noSuchMethod(Invocation.method(
        #getInt,
        [key],
      )) as int?);

  @override
  _i3.Future<bool> setBool(
    String? key,
    bool? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setBool,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  bool? getBool(String? key) => (super.noSuchMethod(Invocation.method(
        #getBool,
        [key],
      )) as bool?);

  @override
  _i3.Future<bool> setDouble(
    String? key,
    double? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setDouble,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  double? getDouble(String? key) => (super.noSuchMethod(Invocation.method(
        #getDouble,
        [key],
      )) as double?);

  @override
  _i3.Future<bool> setStringList(
    String? key,
    List<String>? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setStringList,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  List<String>? getStringList(String? key) =>
      (super.noSuchMethod(Invocation.method(
        #getStringList,
        [key],
      )) as List<String>?);

  @override
  _i3.Future<bool> setJson(
    String? key,
    Map<String, dynamic>? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setJson,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  Map<String, dynamic>? getJson(String? key) =>
      (super.noSuchMethod(Invocation.method(
        #getJson,
        [key],
      )) as Map<String, dynamic>?);

  @override
  _i3.Future<bool> remove(String? key) => (super.noSuchMethod(
        Invocation.method(
          #remove,
          [key],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> clear() => (super.noSuchMethod(
        Invocation.method(
          #clear,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  bool containsKey(String? key) => (super.noSuchMethod(
        Invocation.method(
          #containsKey,
          [key],
        ),
        returnValue: false,
      ) as bool);

  @override
  Set<String> getKeys() => (super.noSuchMethod(
        Invocation.method(
          #getKeys,
          [],
        ),
        returnValue: <String>{},
      ) as Set<String>);

  @override
  _i3.Future<bool> setUserToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #setUserToken,
          [token],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setUserId(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #setUserId,
          [userId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setThemeMode(String? themeMode) => (super.noSuchMethod(
        Invocation.method(
          #setThemeMode,
          [themeMode],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setOnboardingCompleted(bool? completed) =>
      (super.noSuchMethod(
        Invocation.method(
          #setOnboardingCompleted,
          [completed],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  bool getOnboardingCompleted() => (super.noSuchMethod(
        Invocation.method(
          #getOnboardingCompleted,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i3.Future<bool> setBiometricEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setBiometricEnabled,
          [enabled],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  bool getBiometricEnabled() => (super.noSuchMethod(
        Invocation.method(
          #getBiometricEnabled,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i3.Future<bool> setNotificationsEnabled(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setNotificationsEnabled,
          [enabled],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  bool getNotificationsEnabled() => (super.noSuchMethod(
        Invocation.method(
          #getNotificationsEnabled,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i3.Future<bool> clearUserData() => (super.noSuchMethod(
        Invocation.method(
          #clearUserData,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);
}

/// A class which mocks [StripeService].
///
/// See the documentation for Mockito's code generation for more information.
class MockStripeService extends _i1.Mock implements _i18.StripeService {
  MockStripeService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<_i10.PaymentIntent> createPaymentIntent(
          _i10.CreatePaymentIntentRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #createPaymentIntent,
          [request],
        ),
        returnValue: _i3.Future<_i10.PaymentIntent>.value(_FakePaymentIntent_20(
          this,
          Invocation.method(
            #createPaymentIntent,
            [request],
          ),
        )),
      ) as _i3.Future<_i10.PaymentIntent>);

  @override
  _i3.Future<_i10.PaymentIntent> confirmPaymentIntent(
    String? paymentIntentId,
    String? paymentMethodId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #confirmPaymentIntent,
          [
            paymentIntentId,
            paymentMethodId,
          ],
        ),
        returnValue: _i3.Future<_i10.PaymentIntent>.value(_FakePaymentIntent_20(
          this,
          Invocation.method(
            #confirmPaymentIntent,
            [
              paymentIntentId,
              paymentMethodId,
            ],
          ),
        )),
      ) as _i3.Future<_i10.PaymentIntent>);

  @override
  _i3.Future<_i10.PaymentIntent> getPaymentIntent(String? paymentIntentId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getPaymentIntent,
          [paymentIntentId],
        ),
        returnValue: _i3.Future<_i10.PaymentIntent>.value(_FakePaymentIntent_20(
          this,
          Invocation.method(
            #getPaymentIntent,
            [paymentIntentId],
          ),
        )),
      ) as _i3.Future<_i10.PaymentIntent>);

  @override
  _i3.Future<_i10.StripeCustomer> createCustomer(
          _i10.CreateCustomerRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCustomer,
          [request],
        ),
        returnValue:
            _i3.Future<_i10.StripeCustomer>.value(_FakeStripeCustomer_21(
          this,
          Invocation.method(
            #createCustomer,
            [request],
          ),
        )),
      ) as _i3.Future<_i10.StripeCustomer>);

  @override
  _i3.Future<_i10.StripeCustomer> getCustomer(String? customerId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCustomer,
          [customerId],
        ),
        returnValue:
            _i3.Future<_i10.StripeCustomer>.value(_FakeStripeCustomer_21(
          this,
          Invocation.method(
            #getCustomer,
            [customerId],
          ),
        )),
      ) as _i3.Future<_i10.StripeCustomer>);

  @override
  _i3.Future<List<_i10.PaymentMethod>> getCustomerPaymentMethods(
          String? customerId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCustomerPaymentMethods,
          [customerId],
        ),
        returnValue:
            _i3.Future<List<_i10.PaymentMethod>>.value(<_i10.PaymentMethod>[]),
      ) as _i3.Future<List<_i10.PaymentMethod>>);

  @override
  _i3.Future<void> presentPaymentSheet() => (super.noSuchMethod(
        Invocation.method(
          #presentPaymentSheet,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> initPaymentSheet({
    required String? paymentIntentClientSecret,
    String? customerId,
    String? customerEphemeralKeySecret,
    String? merchantDisplayName,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #initPaymentSheet,
          [],
          {
            #paymentIntentClientSecret: paymentIntentClientSecret,
            #customerId: customerId,
            #customerEphemeralKeySecret: customerEphemeralKeySecret,
            #merchantDisplayName: merchantDisplayName,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  bool verifyWebhookSignature(
    String? payload,
    String? signature,
    String? endpointSecret,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyWebhookSignature,
          [
            payload,
            signature,
            endpointSecret,
          ],
        ),
        returnValue: false,
      ) as bool);
}

/// A class which mocks [StripeIssuingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockStripeIssuingService extends _i1.Mock
    implements _i19.StripeIssuingService {
  MockStripeIssuingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<Map<String, dynamic>> createCardholder({
    required String? name,
    required String? email,
    required String? phoneNumber,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCardholder,
          [],
          {
            #name: name,
            #email: email,
            #phoneNumber: phoneNumber,
            #metadata: metadata,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createCard({
    required String? cardholderId,
    required String? currency,
    required _i20.VirtualCardType? type,
    Map<String, dynamic>? spendingControls,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCard,
          [],
          {
            #cardholderId: cardholderId,
            #currency: currency,
            #type: type,
            #spendingControls: spendingControls,
            #metadata: metadata,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCard(String? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCard,
          [cardId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateCard({
    required String? cardId,
    String? status,
    Map<String, dynamic>? spendingControls,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCard,
          [],
          {
            #cardId: cardId,
            #status: status,
            #spendingControls: spendingControls,
            #metadata: metadata,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCardDetails(String? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardDetails,
          [cardId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<List<Map<String, dynamic>>> listCards({
    String? cardholderId,
    String? status,
    int? limit = 10,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #listCards,
          [],
          {
            #cardholderId: cardholderId,
            #status: status,
            #limit: limit,
          },
        ),
        returnValue: _i3.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i3.Future<List<Map<String, dynamic>>>);

  @override
  _i3.Future<Map<String, dynamic>> createAuthorization({
    required String? cardId,
    required int? amount,
    required String? currency,
    required String? merchantName,
    required String? merchantCategory,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createAuthorization,
          [],
          {
            #cardId: cardId,
            #amount: amount,
            #currency: currency,
            #merchantName: merchantName,
            #merchantCategory: merchantCategory,
            #metadata: metadata,
          },
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);
}

/// A class which mocks [NotificationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationService extends _i1.Mock
    implements _i21.NotificationService {
  MockNotificationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> subscribeToTopic(String? topic) => (super.noSuchMethod(
        Invocation.method(
          #subscribeToTopic,
          [topic],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> unsubscribeFromTopic(String? topic) => (super.noSuchMethod(
        Invocation.method(
          #unsubscribeFromTopic,
          [topic],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> showNotification({
    required int? id,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #showNotification,
          [],
          {
            #id: id,
            #title: title,
            #body: body,
            #data: data,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> cancelNotification(int? id) => (super.noSuchMethod(
        Invocation.method(
          #cancelNotification,
          [id],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> cancelAllNotifications() => (super.noSuchMethod(
        Invocation.method(
          #cancelAllNotifications,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

/// A class which mocks [RealtimeService].
///
/// See the documentation for Mockito's code generation for more information.
class MockRealtimeService extends _i1.Mock implements _i22.RealtimeService {
  MockRealtimeService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<_i22.WalletRealtimeEvent> subscribeToWallet(String? walletId) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribeToWallet,
          [walletId],
        ),
        returnValue: _i3.Stream<_i22.WalletRealtimeEvent>.empty(),
      ) as _i3.Stream<_i22.WalletRealtimeEvent>);

  @override
  _i3.Stream<_i22.NotificationRealtimeEvent> subscribeToNotifications(
          String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribeToNotifications,
          [userId],
        ),
        returnValue: _i3.Stream<_i22.NotificationRealtimeEvent>.empty(),
      ) as _i3.Stream<_i22.NotificationRealtimeEvent>);

  @override
  _i3.Stream<_i22.CardRealtimeEvent> subscribeToCards(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribeToCards,
          [userId],
        ),
        returnValue: _i3.Stream<_i22.CardRealtimeEvent>.empty(),
      ) as _i3.Stream<_i22.CardRealtimeEvent>);

  @override
  _i3.Stream<_i22.MessageRealtimeEvent> subscribeToChat(String? walletId) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribeToChat,
          [walletId],
        ),
        returnValue: _i3.Stream<_i22.MessageRealtimeEvent>.empty(),
      ) as _i3.Stream<_i22.MessageRealtimeEvent>);

  @override
  void unsubscribe(String? channelName) => super.noSuchMethod(
        Invocation.method(
          #unsubscribe,
          [channelName],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unsubscribeAll() => super.noSuchMethod(
        Invocation.method(
          #unsubscribeAll,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ChatService].
///
/// See the documentation for Mockito's code generation for more information.
class MockChatService extends _i1.Mock implements _i11.ChatService {
  MockChatService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i11.ChatMessage> sendMessage({
    required String? walletId,
    required String? content,
    String? replyToId,
    _i11.ChatMessageType? type = _i11.ChatMessageType.text,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendMessage,
          [],
          {
            #walletId: walletId,
            #content: content,
            #replyToId: replyToId,
            #type: type,
            #metadata: metadata,
          },
        ),
        returnValue: _i3.Future<_i11.ChatMessage>.value(_FakeChatMessage_22(
          this,
          Invocation.method(
            #sendMessage,
            [],
            {
              #walletId: walletId,
              #content: content,
              #replyToId: replyToId,
              #type: type,
              #metadata: metadata,
            },
          ),
        )),
      ) as _i3.Future<_i11.ChatMessage>);

  @override
  _i3.Future<List<_i11.ChatMessage>> getMessages({
    required String? walletId,
    int? limit = 50,
    String? before,
    String? after,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMessages,
          [],
          {
            #walletId: walletId,
            #limit: limit,
            #before: before,
            #after: after,
          },
        ),
        returnValue:
            _i3.Future<List<_i11.ChatMessage>>.value(<_i11.ChatMessage>[]),
      ) as _i3.Future<List<_i11.ChatMessage>>);

  @override
  _i3.Future<_i11.ChatMessage> updateMessage({
    required String? messageId,
    required String? content,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMessage,
          [],
          {
            #messageId: messageId,
            #content: content,
            #metadata: metadata,
          },
        ),
        returnValue: _i3.Future<_i11.ChatMessage>.value(_FakeChatMessage_22(
          this,
          Invocation.method(
            #updateMessage,
            [],
            {
              #messageId: messageId,
              #content: content,
              #metadata: metadata,
            },
          ),
        )),
      ) as _i3.Future<_i11.ChatMessage>);

  @override
  _i3.Future<void> deleteMessage(String? messageId) => (super.noSuchMethod(
        Invocation.method(
          #deleteMessage,
          [messageId],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> markMessagesAsRead({
    required String? walletId,
    required List<String>? messageIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #markMessagesAsRead,
          [],
          {
            #walletId: walletId,
            #messageIds: messageIds,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<int> getUnreadCount(String? walletId) => (super.noSuchMethod(
        Invocation.method(
          #getUnreadCount,
          [walletId],
        ),
        returnValue: _i3.Future<int>.value(0),
      ) as _i3.Future<int>);

  @override
  _i3.Future<void> reactToMessage({
    required String? messageId,
    required String? emoji,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #reactToMessage,
          [],
          {
            #messageId: messageId,
            #emoji: emoji,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> removeReaction({
    required String? messageId,
    required String? emoji,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeReaction,
          [],
          {
            #messageId: messageId,
            #emoji: emoji,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

/// A class which mocks [WebhookService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebhookService extends _i1.Mock implements _i23.WebhookService {
  MockWebhookService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> handleStripeWebhook(_i10.WebhookEvent? event) =>
      (super.noSuchMethod(
        Invocation.method(
          #handleStripeWebhook,
          [event],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

/// A class which mocks [FraudPreventionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFraudPreventionService extends _i1.Mock
    implements _i12.FraudPreventionService {
  MockFraudPreventionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i12.RiskAssessment> assessTransactionRisk(
          _i12.TransactionData? transaction) =>
      (super.noSuchMethod(
        Invocation.method(
          #assessTransactionRisk,
          [transaction],
        ),
        returnValue:
            _i3.Future<_i12.RiskAssessment>.value(_FakeRiskAssessment_23(
          this,
          Invocation.method(
            #assessTransactionRisk,
            [transaction],
          ),
        )),
      ) as _i3.Future<_i12.RiskAssessment>);

  @override
  _i3.Future<void> recordTransaction(_i12.TransactionData? transaction) =>
      (super.noSuchMethod(
        Invocation.method(
          #recordTransaction,
          [transaction],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> recordFailedAttempt(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #recordFailedAttempt,
          [userId],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> isUserBlocked(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #isUserBlocked,
          [userId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<double> getUserRiskScore(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #getUserRiskScore,
          [userId],
        ),
        returnValue: _i3.Future<double>.value(0.0),
      ) as _i3.Future<double>);

  @override
  _i3.Future<void> clearUserFraudData(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #clearUserFraudData,
          [userId],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

/// A class which mocks [SecurityAuditService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSecurityAuditService extends _i1.Mock
    implements _i13.SecurityAuditService {
  MockSecurityAuditService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> logAuditEvent({
    required String? eventType,
    required String? userId,
    String? sessionId,
    required String? description,
    Map<String, dynamic>? metadata,
    String? severity = 'medium',
    String? ipAddress,
    String? userAgent,
    String? deviceId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logAuditEvent,
          [],
          {
            #eventType: eventType,
            #userId: userId,
            #sessionId: sessionId,
            #description: description,
            #metadata: metadata,
            #severity: severity,
            #ipAddress: ipAddress,
            #userAgent: userAgent,
            #deviceId: deviceId,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> retryFailedAuditEvents() => (super.noSuchMethod(
        Invocation.method(
          #retryFailedAuditEvents,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<_i13.ComplianceStatus> performComplianceAssessment() =>
      (super.noSuchMethod(
        Invocation.method(
          #performComplianceAssessment,
          [],
        ),
        returnValue:
            _i3.Future<_i13.ComplianceStatus>.value(_FakeComplianceStatus_24(
          this,
          Invocation.method(
            #performComplianceAssessment,
            [],
          ),
        )),
      ) as _i3.Future<_i13.ComplianceStatus>);

  @override
  _i3.Future<_i13.ComplianceStatus?> getComplianceStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #getComplianceStatus,
          [],
        ),
        returnValue: _i3.Future<_i13.ComplianceStatus?>.value(),
      ) as _i3.Future<_i13.ComplianceStatus?>);

  @override
  _i3.Future<Map<String, dynamic>> generateSecurityReport() =>
      (super.noSuchMethod(
        Invocation.method(
          #generateSecurityReport,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<void> clearOldAuditData() => (super.noSuchMethod(
        Invocation.method(
          #clearOldAuditData,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

/// A class which mocks [ApiClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiClient extends _i1.Mock implements _i24.ApiClient {
  MockApiClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<Map<String, dynamic>> createPaymentIntent(
          Map<String, dynamic>? data) =>
      (super.noSuchMethod(
        Invocation.method(
          #createPaymentIntent,
          [data],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> confirmPaymentIntent(
    String? paymentIntentId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #confirmPaymentIntent,
          [
            paymentIntentId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createCard(Map<String, dynamic>? data) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCard,
          [data],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCard(String? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCard,
          [cardId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateCard(
    String? cardId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCard,
          [
            cardId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> listCards(
    int? limit,
    String? startingAfter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #listCards,
          [
            limit,
            startingAfter,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createCardholder(
          Map<String, dynamic>? data) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCardholder,
          [data],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCardholder(String? cardholderId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardholder,
          [cardholderId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateCardholder(
    String? cardholderId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCardholder,
          [
            cardholderId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> listTransactions(
    String? cardId,
    String? cardholderId,
    int? limit,
    String? startingAfter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #listTransactions,
          [
            cardId,
            cardholderId,
            limit,
            startingAfter,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getTransaction(String? transactionId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTransaction,
          [transactionId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateTransaction(
    String? transactionId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateTransaction,
          [
            transactionId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> listAuthorizations(
    String? cardId,
    String? cardholderId,
    int? limit,
    String? startingAfter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #listAuthorizations,
          [
            cardId,
            cardholderId,
            limit,
            startingAfter,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getAuthorization(String? authorizationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAuthorization,
          [authorizationId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> approveAuthorization(
    String? authorizationId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #approveAuthorization,
          [
            authorizationId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> declineAuthorization(
    String? authorizationId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineAuthorization,
          [
            authorizationId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createCustomer(Map<String, dynamic>? data) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCustomer,
          [data],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getCustomer(String? customerId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCustomer,
          [customerId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> updateCustomer(
    String? customerId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCustomer,
          [
            customerId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createPaymentMethod(
          Map<String, dynamic>? data) =>
      (super.noSuchMethod(
        Invocation.method(
          #createPaymentMethod,
          [data],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> getPaymentMethod(String? paymentMethodId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getPaymentMethod,
          [paymentMethodId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> attachPaymentMethod(
    String? paymentMethodId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #attachPaymentMethod,
          [
            paymentMethodId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> detachPaymentMethod(
          String? paymentMethodId) =>
      (super.noSuchMethod(
        Invocation.method(
          #detachPaymentMethod,
          [paymentMethodId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> createSetupIntent(
          Map<String, dynamic>? data) =>
      (super.noSuchMethod(
        Invocation.method(
          #createSetupIntent,
          [data],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<String, dynamic>> confirmSetupIntent(
    String? setupIntentId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #confirmSetupIntent,
          [
            setupIntentId,
            data,
          ],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);
}
