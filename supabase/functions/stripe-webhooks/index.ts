import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Stripe webhook endpoint secret
const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')!

interface StripeEvent {
  id: string
  type: string
  data: {
    object: any
  }
  created: number
  livemode: boolean
}

// Verify Stripe webhook signature
async function verifyStripeSignature(
  payload: string,
  signature: string,
  secret: string
): Promise<boolean> {
  try {
    const elements = signature.split(',')
    const signatureElements: { [key: string]: string } = {}

    for (const element of elements) {
      const [key, value] = element.split('=')
      signatureElements[key] = value
    }

    const timestamp = signatureElements['t']
    const v1 = signatureElements['v1']

    if (!timestamp || !v1) {
      return false
    }

    // Create expected signature
    const payloadForSigning = `${timestamp}.${payload}`
    const key = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )

    const signature_bytes = await crypto.subtle.sign(
      'HMAC',
      key,
      new TextEncoder().encode(payloadForSigning)
    )

    const expectedSignature = Array.from(new Uint8Array(signature_bytes))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')

    // Compare signatures
    return expectedSignature === v1
  } catch (error) {
    console.error('Signature verification error:', error)
    return false
  }
}

// Process webhook events
async function processWebhookEvent(event: StripeEvent): Promise<void> {
  switch (event.type) {
    case 'payment_intent.succeeded':
      await handlePaymentIntentSucceeded(event)
      break
    case 'payment_intent.payment_failed':
      await handlePaymentIntentFailed(event)
      break
    case 'payment_intent.canceled':
      await handlePaymentIntentCanceled(event)
      break
    case 'customer.created':
      await handleCustomerCreated(event)
      break
    case 'customer.updated':
      await handleCustomerUpdated(event)
      break
    case 'payment_method.attached':
      await handlePaymentMethodAttached(event)
      break
    case 'issuing_card.created':
      await handleIssuingCardCreated(event)
      break
    case 'issuing_authorization.created':
      await handleIssuingAuthorizationCreated(event)
      break
    default:
      console.log(`Unhandled webhook event type: ${event.type}`)
  }
}

// Handle payment intent succeeded
async function handlePaymentIntentSucceeded(event: StripeEvent): Promise<void> {
  const paymentIntent = event.data.object
  const metadata = paymentIntent.metadata || {}

  const walletId = metadata.wallet_id
  const userId = metadata.user_id
  const transactionType = metadata.transaction_type || 'contribution'

  if (!walletId || !userId) {
    console.error('Missing wallet_id or user_id in payment intent metadata')
    return
  }

  // Create transaction record
  const { error } = await supabase.from('transactions').insert({
    wallet_id: walletId,
    user_id: userId,
    type: transactionType,
    status: 'completed',
    amount: paymentIntent.amount / 100.0, // Convert from cents to dollars
    currency: paymentIntent.currency,
    description: 'Wallet contribution',
    stripe_payment_intent_id: paymentIntent.id,
    metadata: metadata,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  })

  if (error) {
    console.error('Error creating transaction:', error)
    throw new Error(`Failed to create transaction: ${error.message}`)
  }

  // Send notification
  await sendNotification({
    userId: userId,
    type: 'payment',
    title: 'Payment Successful',
    body: `Your payment of $${(paymentIntent.amount / 100.0).toFixed(2)} was processed successfully.`,
    data: {
      wallet_id: walletId,
      amount: paymentIntent.amount / 100.0,
      payment_intent_id: paymentIntent.id,
    },
  })

  console.log(`Payment intent succeeded: ${paymentIntent.id}`)
}

// Handle payment intent failed
async function handlePaymentIntentFailed(event: StripeEvent): Promise<void> {
  const paymentIntent = event.data.object
  const metadata = paymentIntent.metadata || {}

  const userId = metadata.user_id
  if (!userId) return

  // Send notification
  await sendNotification({
    userId: userId,
    type: 'payment',
    title: 'Payment Failed',
    body: `Your payment of $${(paymentIntent.amount / 100.0).toFixed(2)} could not be processed.`,
    data: {
      payment_intent_id: paymentIntent.id,
      error: paymentIntent.last_payment_error?.message || 'Unknown error',
    },
  })

  console.log(`Payment intent failed: ${paymentIntent.id}`)
}

// Handle payment intent canceled
async function handlePaymentIntentCanceled(event: StripeEvent): Promise<void> {
  const paymentIntent = event.data.object
  const metadata = paymentIntent.metadata || {}

  const userId = metadata.user_id
  if (!userId) return

  // Send notification
  await sendNotification({
    userId: userId,
    type: 'payment',
    title: 'Payment Canceled',
    body: `Your payment of $${(paymentIntent.amount / 100.0).toFixed(2)} was canceled.`,
    data: {
      payment_intent_id: paymentIntent.id,
    },
  })

  console.log(`Payment intent canceled: ${paymentIntent.id}`)
}

// Handle customer created
async function handleCustomerCreated(event: StripeEvent): Promise<void> {
  const customer = event.data.object
  const metadata = customer.metadata || {}

  const userId = metadata.user_id
  if (!userId) return

  // Update user with Stripe customer ID
  const { error } = await supabase
    .from('users')
    .update({
      stripe_customer_id: customer.id,
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId)

  if (error) {
    console.error('Error updating user with customer ID:', error)
    throw new Error(`Failed to update user: ${error.message}`)
  }

  console.log(`Customer created: ${customer.id} for user: ${userId}`)
}

// Handle customer updated
async function handleCustomerUpdated(event: StripeEvent): Promise<void> {
  const customer = event.data.object
  console.log(`Customer updated: ${customer.id}`)
  // Add any specific customer update logic here if needed
}

// Handle payment method attached
async function handlePaymentMethodAttached(event: StripeEvent): Promise<void> {
  const paymentMethod = event.data.object
  const customerId = paymentMethod.customer

  if (!customerId) return

  // Find user by customer ID
  const { data: user, error } = await supabase
    .from('users')
    .select('id')
    .eq('stripe_customer_id', customerId)
    .maybeSingle()

  if (error || !user) {
    console.error('Error finding user by customer ID:', error)
    return
  }

  // Send notification
  await sendNotification({
    userId: user.id,
    type: 'card',
    title: 'Payment Method Added',
    body: 'A new payment method has been added to your account.',
    data: { customer_id: customerId },
  })

  console.log(`Payment method attached: ${paymentMethod.id} for customer: ${customerId}`)
}

// Handle issuing card created
async function handleIssuingCardCreated(event: StripeEvent): Promise<void> {
  const card = event.data.object
  const metadata = card.metadata || {}

  const walletId = metadata.wallet_id
  const userId = metadata.user_id

  if (!walletId || !userId) return

  // Update card record in database
  const { error } = await supabase
    .from('cards')
    .update({
      stripe_card_id: card.id,
      stripe_cardholder_id: card.cardholder,
      status: 'active',
      updated_at: new Date().toISOString(),
    })
    .eq('wallet_id', walletId)
    .eq('user_id', userId)

  if (error) {
    console.error('Error updating card:', error)
    throw new Error(`Failed to update card: ${error.message}`)
  }

  // Send notification
  await sendNotification({
    userId: userId,
    type: 'card',
    title: 'Virtual Card Ready',
    body: 'Your virtual card has been created and is ready to use.',
    data: {
      wallet_id: walletId,
      card_id: card.id,
    },
  })

  console.log(`Issuing card created: ${card.id}`)
}

// Handle issuing authorization created (transaction)
async function handleIssuingAuthorizationCreated(event: StripeEvent): Promise<void> {
  const authorization = event.data.object
  const card = authorization.card
  const metadata = card.metadata || {}

  const walletId = metadata.wallet_id
  const userId = metadata.user_id
  const amount = authorization.amount
  const merchantData = authorization.merchant_data

  if (!walletId || !userId) return

  // Create transaction record
  const { error } = await supabase.from('transactions').insert({
    wallet_id: walletId,
    user_id: userId,
    type: 'spending',
    status: authorization.approved ? 'completed' : 'declined',
    amount: amount / 100.0, // Convert from cents to dollars
    currency: authorization.currency,
    description: `Card transaction at ${merchantData?.name || 'merchant'}`,
    merchant_name: merchantData?.name,
    merchant_category: merchantData?.category,
    stripe_transaction_id: authorization.id,
    metadata: {
      authorization_id: authorization.id,
      card_id: card.id,
      merchant_data: merchantData,
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  })

  if (error) {
    console.error('Error creating transaction:', error)
    throw new Error(`Failed to create transaction: ${error.message}`)
  }

  // Send notification
  await sendNotification({
    userId: userId,
    type: 'transaction',
    title: 'Card Transaction',
    body: `You spent $${(amount / 100.0).toFixed(2)} at ${merchantData?.name || 'a merchant'}.`,
    data: {
      wallet_id: walletId,
      amount: amount / 100.0,
      merchant: merchantData?.name,
    },
  })

  console.log(`Issuing authorization created: ${authorization.id}`)
}

// Send notification helper
async function sendNotification({
  userId,
  type,
  title,
  body,
  data,
}: {
  userId: string
  type: string
  title: string
  body: string
  data?: any
}): Promise<void> {
  const { error } = await supabase.from('notifications').insert({
    user_id: userId,
    type: type,
    title: title,
    body: body,
    data: data,
    is_read: false,
    created_at: new Date().toISOString(),
  })

  if (error) {
    console.error('Error sending notification:', error)
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', {
      status: 405,
      headers: corsHeaders
    })
  }

  try {
    const signature = req.headers.get('stripe-signature')
    const body = await req.text()

    if (!signature) {
      throw new Error('Missing stripe-signature header')
    }

    // Verify webhook signature
    const isValid = await verifyStripeSignature(body, signature, webhookSecret)
    if (!isValid) {
      throw new Error('Invalid webhook signature')
    }

    // Parse the event
    const event: StripeEvent = JSON.parse(body)

    console.log(`Processing webhook event: ${event.type} (${event.id})`)

    // Process the webhook event
    await processWebhookEvent(event)

    return new Response(JSON.stringify({ received: true, eventId: event.id }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Webhook processing error:', error)
    return new Response(JSON.stringify({
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    })
  }
})
